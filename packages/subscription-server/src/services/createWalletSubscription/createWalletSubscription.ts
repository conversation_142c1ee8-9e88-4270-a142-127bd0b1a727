import { CountryCode } from '@bp/pulse-common';
import axios, { AxiosRequestConfig } from 'axios';
import { ThreeDs } from 'types/graphql';

import { SCOPE } from '../../config/types/enum';
import { env } from '../../env';
import { SubscriptionServerContext } from '../../types/apollo';
import { SchemeType, UserType } from '../../types/enums';
import { SubscriptionServerError } from '../../types/errors';
import {
  CreateWalletSubscriptionBody,
  DpaasBillingItems,
  OfferType,
} from '../../types/services';
import { requestWithRetry } from '../../utils/retry';
import {
  calculateSubscriptionTrialEndDate,
  checkPartnerType,
} from '../../utils/subscription';
import { isReturningCustomer } from '../../utils/user';
import { tenantConfig } from '../bppay/bppay';
import { checkSetDefaultPaymentMethod } from '../checkSetDefaultPaymentMethod/checkSetDefaultPaymentMethod';
import { getWalletSubscription } from '../getWalletSubscription/getWalletSubscription';
import { validateOffer } from '../offer/validateOffer';
import { createMembershipInternal } from '../user/createMembershipInternal';
import { getUserInfo } from '../user/getUserInfo';
import { getUserSubsPlan } from '../user/getUserSubsPlan';
import { mapAndUpdateTagScheme } from './mapAndUpdateTagInternal';

const isInvalidResponse = (response: any) => {
  return (
    !response ||
    !response.isValid ||
    response.status === '500' ||
    (!response.offer?.subsPlanId &&
      response.offer?.offerType !== OfferType.CREDIT)
  );
};

const constructBillingItems = (
  externalPlanId: string,
  offerPlanId: string | undefined,
) => {
  const billingItems = [{ planId: externalPlanId, billableUnits: 1 }];
  if (offerPlanId) {
    billingItems.push({ planId: offerPlanId, billableUnits: 1 });
  }
  return billingItems;
};

const constructRequestBody = (
  userId: string,
  country: string,
  paymentMethodId: string,
  billingItems: DpaasBillingItems[],
  planDuration: number,
  returningCustomer: boolean,
  threeDS?: ThreeDs,
): CreateWalletSubscriptionBody => {
  const body: CreateWalletSubscriptionBody = {
    customerId: userId,
    merchantId: tenantConfig[country].merchantId,
    paymentMethodId,
    billingItems,
    timeZoneCode: 'America/Chicago',
    ...(threeDS ? threeDS : {}),
  };
  if (!returningCustomer) {
    body.trialEndsOn = calculateSubscriptionTrialEndDate(
      new Date(),
      planDuration,
    );
  }
  return body;
};

export const createWalletSubscription = async (
  ctx: SubscriptionServerContext,
  userId: string,
  paymentMethodId: string,
  offerCode: string | undefined,
  headers: Partial<AxiosRequestConfig>,
  threeDS?: ThreeDs,
) => {
  if (!paymentMethodId) {
    throw new SubscriptionServerError(
      'createWalletSubscription error: paymentMethodId is undefined',
    );
  }

  const userInfo = await getUserInfo(userId);

  if (!userInfo || userInfo.status === '500') {
    throw new SubscriptionServerError(
      'createWalletSubscription error: getUserInfo query failed',
    );
  }
  const { partnerType, type, subsEnabled, membership, country } = userInfo;

  checkPartnerType(partnerType);

  if (!subsEnabled) {
    throw new SubscriptionServerError(
      'createWalletSubscription error: unauthorized, subsEnabled is false or undefined',
    );
  }
  if (type !== UserType.PAYG_WALLET) {
    throw new SubscriptionServerError(
      'createWalletSubscription error: unauthorized, userType is not PAYG-WALLET',
    );
  }

  let offerPlanId;

  if (offerCode) {
    const response = await requestWithRetry(validateOffer, ctx, {
      offerCode,
      userId,
    });

    const invalidResponse = isInvalidResponse(response);
    if (invalidResponse) {
      throw new SubscriptionServerError(
        'createWalletSubscription error: could not get user offer subs plan',
      );
    }

    offerPlanId = response?.offer?.subsPlanId;
  }

  const userSubsPlan = await requestWithRetry(getUserSubsPlan, ctx, userId);

  if (!userSubsPlan || !userSubsPlan.plans || userSubsPlan.status === '500') {
    throw new SubscriptionServerError(
      'createWalletSubscription error: could not get user subs plan',
    );
  }

  const BP_PAY_SUBS_ENDPOINT =
    ctx?.scope === SCOPE.INTEGRATION_TEST_SCOPE
      ? env.MOCK_BP_PAY_SUBS_ENDPOINT
      : env.BP_PAY_SUBS_ENDPOINT;

  const url = `${BP_PAY_SUBS_ENDPOINT}/subscriptions`;
  const { externalPlanId, planDuration } = userSubsPlan.plans[0];

  const returningCustomer = !!(
    isReturningCustomer(membership) &&
    planDuration &&
    planDuration > 0
  );

  const billingItems = constructBillingItems(externalPlanId, offerPlanId);
  const body = constructRequestBody(
    userId,
    country,
    paymentMethodId,
    billingItems,
    planDuration,
    returningCustomer,
    threeDS,
  );

  if (!returningCustomer) {
    const trialEndsOn = calculateSubscriptionTrialEndDate(
      new Date(),
      planDuration,
    );
    body.trialEndsOn = trialEndsOn;
  }

  const authHeaders = headers.headers;

  const createSubscriptionHeaders = {
    headers: {
      ...authHeaders,
      ...tenantConfig[country],
    },
  };

  if (!returningCustomer) {
    await createSubscription(
      ctx,
      url,
      body,
      createSubscriptionHeaders,
      userId,
      paymentMethodId,
    );

    return {
      status: '200',
    };
  }

  createSubscription(
    ctx,
    url,
    body,
    createSubscriptionHeaders,
    userId,
    paymentMethodId,
  );

  return {
    status: '200',
  };
};

const createSubscription = async (
  ctx: SubscriptionServerContext,
  url: string,
  body: CreateWalletSubscriptionBody,
  createSubscriptionHeaders: Partial<AxiosRequestConfig>,
  userId: string,
  paymentMethodId: string,
) => {
  try {
    const userInfo = await getUserInfo(userId);

    ctx.logger.info(
      `Creating subscription for userId ${userId}, country ${userInfo?.country}`,
    );

    if (
      env.ENABLE_DE_SUBSCRIPTIONS_ENTITLEMENT === 'true' &&
      (userInfo.country === CountryCode.DE ||
        userInfo.country === CountryCode.UK) &&
      userInfo.tagIds
    ) {
      const mapCountryToScheme = {
        [CountryCode.DE]: SchemeType.DE_EUROPEAN_SUBS_SCHEME,
        [CountryCode.UK]: SchemeType.UK_SUBS_SCHEME,
      };
      ctx.logger.info(
        `Updating userId ${userId} tags to scheme ${
          mapCountryToScheme[userInfo.country]
        }`,
      );
      await mapAndUpdateTagScheme(
        userInfo,
        userId,
        mapCountryToScheme[userInfo.country],
        ctx.logger,
      ).catch((err) =>
        ctx.logger.error('Error updating user tags scheme: ', err),
      );
    }

    const response = await axios
      .post(url, body, createSubscriptionHeaders)
      .catch(async (err) => {
        ctx.logger.error(
          `createWalletSubscriptionError - Error updating user tag scheme: `,
          err,
        );
        if (env.ENABLE_DE_SUBSCRIPTIONS_ENTITLEMENT === 'true') {
          await mapAndUpdateTagScheme(
            userInfo,
            userId,
            userInfo.schemes?.[0].schemeName,
            ctx.logger,
          ).catch((err) =>
            ctx.logger.error(
              'Error on rolling back tariff for user after createSubscription failure: ',
              err,
            ),
          );
        }
      });

    const createdSubscription = await getWalletSubscription(
      ctx,
      response?.data?.subscriptionId,
      createSubscriptionHeaders,
    );

    if (!createdSubscription) {
      ctx.logger.error(
        'createWalletSubscription error: could not get subscription after it was created',
      );
      return;
    }

    await checkSetDefaultPaymentMethod(ctx, userId, paymentMethodId);

    const { nextBillingDate, id } = createdSubscription;

    await createMembershipInternal(userId, true, nextBillingDate, id);

    ctx.logger.info(
      `Subscription created successfully: ${response?.data?.subscriptionId}`,
    );
  } catch (e) {
    ctx.logger.error(
      `🚨 Error encountered - createWalletSubscription error: ${JSON.stringify(
        e,
      )}`,
    );
  }
};
