import { getDPaaSHeaders } from '../../services/bppay/bppay';
import { createWalletSubscription } from '../../services/createWalletSubscription/createWalletSubscription';
import { ResolverFn } from '../../types/apollo';
import { SubscriptionClientError } from '../../types/errors';
import {
  CreateWalletSubscriptionResponse,
  MutationCreateWalletSubscriptionArgs,
} from '../../types/graphql';

export const createWalletSubscriptionMutation: ResolverFn<
  unknown,
  MutationCreateWalletSubscriptionArgs,
  CreateWalletSubscriptionResponse
> = async (_, args, ctx) => {
  if (!args.paymentMethodId) {
    throw new SubscriptionClientError(
      'createWalletSubscription error: No paymentMethodId provided',
    );
  }
  if (!args.userId) {
    throw new SubscriptionClientError(
      'createWalletSubscription error: No userId provided',
    );
  }
  const headers = await getDPaaSHeaders(args.country ?? undefined);
  return createWalletSubscription(
    ctx,
    args.userId,
    args.paymentMethodId,
    args.offerCode ?? undefined,
    headers,
    args.threeDS ?? undefined,
  ).catch((error) => {
    ctx.logger.error('createWalletSubscription error: ', {
      reason: error.message,
      error,
    });
    return {
      status: error.extensions.code || '500',
      error: error.message,
      message: 'createWalletSubscription failed',
    };
  });
};

export const resolver = {
  Mutation: {
    createWalletSubscription: createWalletSubscriptionMutation,
  },
};
