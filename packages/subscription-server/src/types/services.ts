import { BillingCycle, Currency, PlanType } from './enums';
import { PaymentStatusEnum, ThreeDs } from './graphql';

export interface BillingAmount {
  amount: number;
  currency: Currency;
}

export interface CreatePlanDpass {
  billingAmount: string;
  billingCycle: BillingCycle;
  billingCycleCount: number;
  billingFrequency: number;
  createdAt: string;
  currency: string;
  id: string;
  name: string;
  planType: PlanType;
}

export interface PlanDpassResponseData {
  data: Omit<CreatePlanDpass, 'billingAmount'> & {
    accountId: string;
    billingAmount: BillingAmount;
  };
}

export interface SubsHistoryRecord {
  transactionDate?: string;
  priceDetails: PriceDetails;
  subscriptionExternalId: string;
  country: string;
  partnerType?: string;
  nextBillingCycleDate?: string;
  cardDetails?: CardDetails;
  isTrial?: boolean;
  salesPostingDate?: number;
}

interface PriceDetails {
  price_base: string;
  price_net: string;
  price_VAT: string;
  price_tax: string;
  price_discount: string;
  price_gross: string;
  price_full_gross: string;
  currency: string;
}

interface CardDetails {
  card_number: string;
  card_scheme: string;
}
export interface SubscriptionPlan {
  id: string;
  name: string;
  billingAmount: {
    amount: string;
    currency: Currency;
  };
  billingCycle: BillingCycle;
  billingFrequency: number;
  accountId: string;
  createdAt: string;
}

export interface SubsPlanDetails {
  id: string;
  name: string;
  billingAmount: {
    amount: string;
    currency: Currency;
  };
  billingCycle: string;
  billingFrequency: number;
  accountId: string;
  createdAt: string;
  billingCycleCount: number;
}

export interface DpaasBillingItems {
  planId: string;
  billingItemId?: string;
  billableUnits: number;
  billingCycleCount?: number;
}

export interface SubscriptionBillingItems {
  billingItemId: string;
  planId: string;
  name: string;
  amount: string;
  currency: string;
  billingCycleCount: number;
}

export type SubscriptionDetails = {
  id: string;
  customerId: string;
  paymentMethodId: string;
  merchantId: string;
  tenantId: string;
  useCase: string;
  billingItems: Array<SubscriptionBillingItems>;
  createdAt: string;
  status: string;
  statusReason: string;
  nextBillingDate: string;
  cancelledOn: string;
  accountId: string;
  agreementId: string;
  deactivationDate: string;
  discountExpiresAt: string | null;
};

export interface Offer {
  subsPlanId?: string;
  offerType: OfferType;
}

export enum OfferType {
  CREDIT = 'CREDIT',
  SUBS = 'SUBS',
  COMBO = 'COMBO',
}

export interface Membership {
  userType: string;
  membershipExternalId: string;
  membershipStatus: string;
  membershipBillingCycleDate?: string;
  membershipStartDate: string;
}

export interface UpdateMembershipInternalArgs {
  userId: string;
  membershipId: string;
  membershipCancelRequested: boolean;
  billingCycleDate?: string;
  membershipStatus?: string;
  subsFlag?: boolean;
}

export interface Tag {
  tagStatus: string;
  tagCardNumber: string;
  tagId: string;
  tagNotes: string;
}

export interface UserInfoResponse {
  type: string;
  tagIds: Array<Tag>;
  partnerType: string;
  country: string;
  entitlements: {
    subsEnabled: boolean;
  };
  membership?: Array<Membership> | undefined;
  schemes:
    | {
        schemeName: string;
      }[];
  status: string;
  message: string;
}

export interface UserApiResponse {
  userInfo: UserInfoResponse;
}

export interface UserSubsPlan {
  externalPlanId: string;
  planDuration: number;
}

export interface UserSubsPlanResponse {
  plans: Array<UserSubsPlan>;
  status: string;
  message: string;
}

export interface UserSubsPlanApiResponse {
  getSubsPlan: UserSubsPlanResponse;
}

export interface CreateMembershipInternalResponse {
  status: string;
  message: string;
}

export interface UpdateMembershipResponse {
  status: string;
  membership: Partial<Membership>;
}

export interface UpdateMembershipApiResponse {
  updateMembershipInternal: UpdateMembershipResponse;
}

export interface CreateMembershipInternalApiResponse {
  createMembershipInternal: CreateMembershipInternalResponse;
}

export interface UpdateWalletSubscriptionBody {
  paymentMethodId?: string;
  billingItems?: Array<DpaasBillingItems>;
}

export interface CreateWalletSubscriptionBody {
  customerId?: string;
  merchantId?: string | undefined;
  paymentMethodId?: string;
  billingItems?: Array<DpaasBillingItems>;
  timeZoneCode?: string;
  trialEndsOn?: string;
  threeDS?: ThreeDs;
}

export interface ValidateOfferResponse {
  offer: Offer;
  isValid: boolean;
  status: string;
  message: string;
}

export interface ValidateOfferApiResponse {
  validateOffer: ValidateOfferResponse;
}
export interface ApplyOfferResponse {
  error: string;
}

export interface ApplyOfferApiResponse {
  applyOffer: ApplyOfferResponse;
}

export interface SetDefaultPaymentMethodWalletApiResponse {
  setDefaultPaymentMethodWallet: string;
}

export interface DeleteSubsDefaultPaymentMethodWalletResponse {
  message: string;
  status: string;
}

export interface DeleteSubsDefaultPaymentMethodWalletApiResponse {
  deleteSUBSDefaultPaymentMethodWallet: DeleteSubsDefaultPaymentMethodWalletResponse;
}

export interface UpdateSubsDpassBody {
  billingAmount: string;
  billingCycleCount: number;
  currency: string;
  name: string;
  planType: PlanType;
}

export interface SubscriptionRecordDynamo {
  transaction_id: string;
  card_details: {
    card_number: string;
    card_scheme: string;
  };
  payment_status?: PaymentStatusEnum;
  price_details: {
    price_base: string;
    price_net: string;
    price_VAT: string;
    price_discount: string;
    price_gross: string;
    price_full_gross: string;
    price_tax: string;
    currency: string;
  };
  subs_account_id: string;
  transaction_date: string;
  transaction_day: string;
  transaction_month: string;
  user_id: string;
  next_billing_cycle_date: string;
  partner_type: string;
  country: string;
  is_trial: boolean;
  sales_posting_date: number;
  retrieval_reference_number: string;
}
