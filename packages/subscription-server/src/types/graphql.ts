export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: string;
  String: string;
  Boolean: boolean;
  Int: number;
  Float: number;
};

export type AllSubscriptionsInput = {
  endDate: Scalars['String'];
  startDate: Scalars['String'];
};

export type ApplySubscription = {
  __typename?: 'ApplySubscription';
  subscription_code?: Maybe<Scalars['String']>;
};

export type BillingAmount = {
  __typename?: 'BillingAmount';
  amount?: Maybe<Scalars['Float']>;
  currency?: Maybe<Scalars['String']>;
};

export enum BillingCycle {
  MONTHLY = 'MONTHLY'
}

export type CancelSubscriptionResponse = {
  __typename?: 'CancelSubscriptionResponse';
  cancelledOn?: Maybe<Scalars['String']>;
  endOfCurrentBillingCycle?: Maybe<Scalars['String']>;
  error?: Maybe<Scalars['String']>;
  membershipStatus?: Maybe<Scalars['String']>;
  message?: Maybe<Scalars['String']>;
  status?: Maybe<Scalars['String']>;
  statusReason?: Maybe<Scalars['String']>;
};

export type CardDetailsSubscription = {
  __typename?: 'CardDetailsSubscription';
  cardNumber?: Maybe<Scalars['String']>;
  cardScheme?: Maybe<Scalars['String']>;
};

export type CreateWalletSubscriptionResponse = {
  __typename?: 'CreateWalletSubscriptionResponse';
  error?: Maybe<Scalars['String']>;
  status?: Maybe<Scalars['String']>;
};

export type GetAllSubsTransactionsInput = {
  /** Defaults to PAYMENT_CONFIRMED. */
  dateType?: InputMaybe<SubsDateType>;
  /**
   * Pass in ISO type date.
   * 1. ISO string: 2024-08-01T00:00:00.000Z
   *
   * End date can be the same day as start date.
   * End date cannot be before start date.
   * End date and start date have to be in the same month.
   */
  endDate: Scalars['String'];
  lastEvaluatedKey?: InputMaybe<LastEvaluatedKeySubscriptionTransaction>;
  /**
   * Limit if you want to limit the number of results returned.
   * Defaults to no limit.
   */
  limit?: InputMaybe<Scalars['Int']>;
  /**
   * If not provided, all payment statuses will be returned.
   * If provided as null all status will be returned.
   */
  paymentStatus?: InputMaybe<PaymentStatusEnum>;
  /**
   * Pass in ISO type date.
   * 1. ISO string: 2024-08-01T00:00:00.000Z
   *
   * Start and end date have to be in the same month.
   */
  startDate: Scalars['String'];
};

export type GetAllSubsTransactionsResponse = {
  __typename?: 'GetAllSubsTransactionsResponse';
  error?: Maybe<Scalars['String']>;
  /** Last evaluated key to be used in the next query if exists. */
  lastEvaluatedKey?: Maybe<LastEvaluatedKeySubscriptionTransactionUnion>;
  results?: Maybe<Array<Maybe<SubscriptionRecord>>>;
  status?: Maybe<Scalars['String']>;
  totalResultsCount?: Maybe<Scalars['Int']>;
};

export type GetSubscriptionRecordsResponse = {
  __typename?: 'GetSubscriptionRecordsResponse';
  lastKey?: Maybe<LastEvaluatedKeyType>;
  results?: Maybe<Array<Maybe<SubscriptionRecord>>>;
};

export type LastEvaluatedKeyDayIndexType = {
  __typename?: 'LastEvaluatedKeyDayIndexType';
  transaction_day?: Maybe<Scalars['String']>;
  transaction_id?: Maybe<Scalars['String']>;
};

export type LastEvaluatedKeyInput = {
  transaction_id?: InputMaybe<Scalars['String']>;
};

export type LastEvaluatedKeyMonthIndexType = {
  __typename?: 'LastEvaluatedKeyMonthIndexType';
  transaction_id?: Maybe<Scalars['String']>;
  transaction_month?: Maybe<Scalars['String']>;
};

export type LastEvaluatedKeySubscriptionTransaction = {
  transaction_day?: InputMaybe<Scalars['String']>;
  /**
   * Pass only combination of transaction_id and transaction_month
   * or transaction_id and transaction_day.
   *
   * Otherwise will throw an error.
   */
  transaction_id?: InputMaybe<Scalars['String']>;
  transaction_month?: InputMaybe<Scalars['String']>;
};

export type LastEvaluatedKeySubscriptionTransactionUnion = LastEvaluatedKeyDayIndexType | LastEvaluatedKeyMonthIndexType;

export type LastEvaluatedKeyType = {
  __typename?: 'LastEvaluatedKeyType';
  transaction_id?: Maybe<Scalars['String']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  cancelWalletSubscription?: Maybe<CancelSubscriptionResponse>;
  createSubsHistoryRecord?: Maybe<CreateSubsHistoryRecordResponse>;
  createWalletSubsPlan?: Maybe<SubsPlansResponse>;
  createWalletSubscription?: Maybe<CreateWalletSubscriptionResponse>;
  retryWalletSubsPayment?: Maybe<SubsPaymentResponse>;
  updateSubsPlanDpaas?: Maybe<UpdatedSubsPlanResponse>;
  updateSubsSalesPostingDate?: Maybe<UpdateSubsSalesPostingDateResponse>;
  updateWalletSubscription?: Maybe<UpdateSubscriptionDetails>;
  upsertWalletSubscription?: Maybe<UpsertWalletSubscriptionResponse>;
};


export type MutationCancelWalletSubscriptionArgs = {
  userId: Scalars['String'];
};


export type MutationCreateSubsHistoryRecordArgs = {
  paymentStatus: Scalars['String'];
  retrievalReferenceNumber?: InputMaybe<Scalars['String']>;
  subsHistoryRecord: SubsHistoryRecord;
  transactionId: Scalars['String'];
  userId: Scalars['String'];
};


export type MutationCreateWalletSubsPlanArgs = {
  payload?: InputMaybe<SubsPlan>;
};


export type MutationCreateWalletSubscriptionArgs = {
  country?: InputMaybe<Scalars['String']>;
  offerCode?: InputMaybe<Scalars['String']>;
  paymentMethodId: Scalars['String'];
  threeDS?: InputMaybe<ThreeDs>;
  userId?: InputMaybe<Scalars['String']>;
};


export type MutationRetryWalletSubsPaymentArgs = {
  country?: InputMaybe<Scalars['String']>;
  membershipId?: InputMaybe<Scalars['String']>;
  userId?: InputMaybe<Scalars['String']>;
};


export type MutationUpdateSubsPlanDpaasArgs = {
  payload?: InputMaybe<UpdateSubsPlanPayload>;
};


export type MutationUpdateSubsSalesPostingDateArgs = {
  salesPostingDate: Scalars['Float'];
  transactionId: Scalars['String'];
};


export type MutationUpdateWalletSubscriptionArgs = {
  offerSubsPlanId?: InputMaybe<Scalars['String']>;
  paymentMethodId?: InputMaybe<Scalars['String']>;
  userId: Scalars['String'];
};


export type MutationUpsertWalletSubscriptionArgs = {
  offerCode?: InputMaybe<Scalars['String']>;
  paymentMethodId?: InputMaybe<Scalars['String']>;
  userId?: InputMaybe<Scalars['String']>;
};

export enum PaymentStatusEnum {
  ALL = 'ALL',
  CAPTURED = 'CAPTURED',
  FAILED = 'FAILED'
}

export enum PlanType {
  BASE = 'BASE',
  OFFER = 'OFFER'
}

export type PriceDetailsSubscription = {
  __typename?: 'PriceDetailsSubscription';
  currency?: Maybe<Scalars['String']>;
  defaultFee?: Maybe<Scalars['String']>;
  priceBase?: Maybe<Scalars['String']>;
  priceDiscount?: Maybe<Scalars['String']>;
  priceGross?: Maybe<Scalars['String']>;
  priceNet?: Maybe<Scalars['String']>;
  priceTaxRate?: Maybe<Scalars['String']>;
  priceVAT?: Maybe<Scalars['String']>;
};

export type Query = {
  __typename?: 'Query';
  /**
   * Returns all transactions for a given month or day.
   * Results are paginated.
   */
  getAllSubsTransactions?: Maybe<GetAllSubsTransactionsResponse>;
  getSubsWalletHistoryRecord?: Maybe<SubscriptionRecord>;
  getSubsWalletHistoryRecords?: Maybe<GetSubscriptionRecordsResponse>;
  getWalletSubscription?: Maybe<SubscriptionDetails>;
};


export type QueryGetAllSubsTransactionsArgs = {
  input?: InputMaybe<GetAllSubsTransactionsInput>;
};


export type QueryGetSubsWalletHistoryRecordArgs = {
  subsTransactionID: Scalars['String'];
  userId: Scalars['String'];
};


export type QueryGetSubsWalletHistoryRecordsArgs = {
  lastKey?: InputMaybe<LastEvaluatedKeyInput>;
  pageSize?: InputMaybe<Scalars['Int']>;
  userId: Scalars['String'];
};


export type QueryGetWalletSubscriptionArgs = {
  userId?: InputMaybe<Scalars['String']>;
};

export type SubsCardDetails = {
  card_number?: InputMaybe<Scalars['String']>;
  card_scheme?: InputMaybe<Scalars['String']>;
};

export enum SubsDateType {
  PAYMENT_CONFIRMED = 'PAYMENT_CONFIRMED',
  SALES_POSTING = 'SALES_POSTING',
  SALES_POSTING_MISSING = 'SALES_POSTING_MISSING'
}

export type SubsHistoryRecord = {
  cardDetails?: InputMaybe<SubsCardDetails>;
  country?: InputMaybe<Scalars['String']>;
  isTrial?: InputMaybe<Scalars['Boolean']>;
  nextBillingCycleDate?: InputMaybe<Scalars['String']>;
  partnerType?: InputMaybe<Scalars['String']>;
  priceDetails?: InputMaybe<SubsPriceDetails>;
  salesPostingDate?: InputMaybe<Scalars['Float']>;
  subscriptionExternalId?: InputMaybe<Scalars['String']>;
  transactionDate?: InputMaybe<Scalars['String']>;
};

export type SubsPaymentResponse = {
  __typename?: 'SubsPaymentResponse';
  accountId?: Maybe<Scalars['String']>;
  cancelledOn?: Maybe<Scalars['String']>;
  error?: Maybe<Scalars['String']>;
  nextBillingDate?: Maybe<Scalars['String']>;
  status?: Maybe<Scalars['String']>;
  statusReason?: Maybe<Scalars['String']>;
  useCase?: Maybe<Scalars['String']>;
};

export type SubsPlan = {
  billingAmount: Scalars['Float'];
  country?: InputMaybe<Scalars['String']>;
  currency: Scalars['String'];
  default?: InputMaybe<Scalars['Boolean']>;
  description: Scalars['String'];
  duration?: InputMaybe<Scalars['Int']>;
  planName: Scalars['String'];
  planType?: InputMaybe<PlanType>;
  scheme_id?: InputMaybe<Scalars['Int']>;
};

export type SubsPlansResponse = {
  __typename?: 'SubsPlansResponse';
  error?: Maybe<Scalars['String']>;
  planId?: Maybe<Scalars['String']>;
  planName?: Maybe<Scalars['String']>;
};

export type SubsPriceDetails = {
  currency?: InputMaybe<Scalars['String']>;
  price_VAT?: InputMaybe<Scalars['String']>;
  price_base?: InputMaybe<Scalars['String']>;
  price_discount?: InputMaybe<Scalars['String']>;
  price_full_gross?: InputMaybe<Scalars['String']>;
  price_gross?: InputMaybe<Scalars['String']>;
  price_net?: InputMaybe<Scalars['String']>;
  price_tax?: InputMaybe<Scalars['String']>;
};

export type SubscriptionDetails = {
  __typename?: 'SubscriptionDetails';
  accountId?: Maybe<Scalars['String']>;
  agreementId?: Maybe<Scalars['String']>;
  billingItems?: Maybe<Array<Maybe<Subscription_BillingItems>>>;
  cancelledOn?: Maybe<Scalars['String']>;
  createdAt?: Maybe<Scalars['String']>;
  customerId?: Maybe<Scalars['String']>;
  deactivationDate?: Maybe<Scalars['String']>;
  discountExpiresAt?: Maybe<Scalars['String']>;
  error?: Maybe<Scalars['String']>;
  id?: Maybe<Scalars['String']>;
  merchantId?: Maybe<Scalars['String']>;
  nextBillingDate?: Maybe<Scalars['String']>;
  paymentMethodId?: Maybe<Scalars['String']>;
  status?: Maybe<Scalars['String']>;
  statusReason?: Maybe<Scalars['String']>;
  tenantId?: Maybe<Scalars['String']>;
  useCase?: Maybe<Scalars['String']>;
};

export type SubscriptionRecord = {
  __typename?: 'SubscriptionRecord';
  cardDetails?: Maybe<CardDetailsSubscription>;
  country?: Maybe<Scalars['String']>;
  isTrial?: Maybe<Scalars['Boolean']>;
  nextBillingCycleDate?: Maybe<Scalars['String']>;
  partnerType?: Maybe<Scalars['String']>;
  paymentStatus?: Maybe<PaymentStatusEnum>;
  priceDetails?: Maybe<PriceDetailsSubscription>;
  retrievalReferenceNumber?: Maybe<Scalars['String']>;
  salesPostingDate?: Maybe<Scalars['Float']>;
  subsAccountId?: Maybe<Scalars['String']>;
  transactionDate?: Maybe<Scalars['String']>;
  transactionDay?: Maybe<Scalars['String']>;
  transactionId?: Maybe<Scalars['String']>;
  transactionMonth?: Maybe<Scalars['String']>;
  userId?: Maybe<Scalars['String']>;
};

export type Subscription_BillingItems = {
  __typename?: 'Subscription_BillingItems';
  amount?: Maybe<Scalars['String']>;
  billingCycleCount?: Maybe<Scalars['Int']>;
  billingItemId?: Maybe<Scalars['String']>;
  currency?: Maybe<Scalars['String']>;
  name?: Maybe<Scalars['String']>;
  planId?: Maybe<Scalars['String']>;
};

export type ThreeDs = {
  acsTransactionId?: InputMaybe<Scalars['String']>;
  cavv?: InputMaybe<Scalars['String']>;
  dsTransactionId?: InputMaybe<Scalars['String']>;
  eciFlag?: InputMaybe<Scalars['String']>;
  enrolled?: InputMaybe<Scalars['String']>;
  paresStatus?: InputMaybe<Scalars['String']>;
  statusReason?: InputMaybe<Scalars['String']>;
  threeDSServerTransactionId?: InputMaybe<Scalars['String']>;
  threeDSVersion?: InputMaybe<Scalars['String']>;
};

export type UpdateSubsPlanPayload = {
  billingAmount?: InputMaybe<Scalars['Float']>;
  country?: InputMaybe<Scalars['String']>;
  currency?: InputMaybe<Scalars['String']>;
  default: Scalars['Boolean'];
  duration?: InputMaybe<Scalars['Int']>;
  planName?: InputMaybe<Scalars['String']>;
  subscriptionPlanId: Scalars['String'];
};

export type UpdateSubsSalesPostingDateResponse = {
  __typename?: 'UpdateSubsSalesPostingDateResponse';
  message?: Maybe<Scalars['String']>;
  status?: Maybe<Scalars['Int']>;
};

export type UpdateSubscriptionDetails = {
  __typename?: 'UpdateSubscriptionDetails';
  accountId?: Maybe<Scalars['String']>;
  agreementId?: Maybe<Scalars['String']>;
  billingItems?: Maybe<Array<Maybe<UpdateSubscription_BillingItems>>>;
  createdAt?: Maybe<Scalars['String']>;
  customerId?: Maybe<Scalars['String']>;
  deactivationDate?: Maybe<Scalars['String']>;
  discountExpiresAt?: Maybe<Scalars['String']>;
  error?: Maybe<Scalars['String']>;
  id?: Maybe<Scalars['String']>;
  merchantId?: Maybe<Scalars['String']>;
  nextBillingDate?: Maybe<Scalars['String']>;
  paymentMethodId?: Maybe<Scalars['String']>;
  status?: Maybe<Scalars['String']>;
  statusReason?: Maybe<Scalars['String']>;
  tenantId?: Maybe<Scalars['String']>;
  useCase?: Maybe<Scalars['String']>;
};

export type UpdateSubscription_BillingItems = {
  __typename?: 'UpdateSubscription_BillingItems';
  billingItemId?: Maybe<Scalars['String']>;
  planId?: Maybe<Scalars['String']>;
};

export type UpdatedSubsPlanResponse = {
  __typename?: 'UpdatedSubsPlanResponse';
  accountId?: Maybe<Scalars['String']>;
  billingAmount?: Maybe<BillingAmount>;
  billingCycle?: Maybe<BillingCycle>;
  billingCycleCount?: Maybe<Scalars['Int']>;
  billingFrequency?: Maybe<Scalars['Int']>;
  createdAt?: Maybe<Scalars['String']>;
  error?: Maybe<Scalars['String']>;
  message?: Maybe<Scalars['String']>;
  planId?: Maybe<Scalars['String']>;
  planName?: Maybe<Scalars['String']>;
  planType?: Maybe<PlanType>;
  status?: Maybe<Scalars['Int']>;
};

export type UpsertWalletSubscriptionResponse = {
  __typename?: 'UpsertWalletSubscriptionResponse';
  accountId?: Maybe<Scalars['String']>;
  agreementId?: Maybe<Scalars['String']>;
  billingItems?: Maybe<Array<Maybe<Subscription_BillingItems>>>;
  createdAt?: Maybe<Scalars['String']>;
  customerId?: Maybe<Scalars['String']>;
  deactivationDate?: Maybe<Scalars['String']>;
  discountExpiresAt?: Maybe<Scalars['String']>;
  error?: Maybe<Scalars['String']>;
  id?: Maybe<Scalars['String']>;
  merchantId?: Maybe<Scalars['String']>;
  nextBillingDate?: Maybe<Scalars['String']>;
  paymentMethodId?: Maybe<Scalars['String']>;
  status?: Maybe<Scalars['String']>;
  statusReason?: Maybe<Scalars['String']>;
  subscriptionId?: Maybe<Scalars['String']>;
  tenantId?: Maybe<Scalars['String']>;
  useCase?: Maybe<Scalars['String']>;
};

export type CreateSubsHistoryRecordResponse = {
  __typename?: 'createSubsHistoryRecordResponse';
  error?: Maybe<Scalars['String']>;
  status?: Maybe<Scalars['String']>;
  subscriptionExternalId?: Maybe<Scalars['String']>;
  transactionId?: Maybe<Scalars['String']>;
};
