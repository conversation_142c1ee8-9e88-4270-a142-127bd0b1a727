type CreateWalletSubscriptionResponse {
  status: String
  error: String
}

type Mutation {
  createWalletSubscription(
    userId: String
    country: String
    paymentMethodId: String!
      @constraint(pattern: "^[0-9a-zA-Z/-]*$", minLength: 36, maxLength: 36)
    offerCode: String
      @constraint(pattern: "^[0-9a-zA-Z]*$", minLength: 12, maxLength: 16)
    threeDS: ThreeDS
  ): CreateWalletSubscriptionResponse
}
