import {
  Countries,
  SupportedPartners,
  TagNotes,
  TagStatus,
  UserStatus,
  UserTypes,
} from '../../common/enums';
import { type UserInfo, Providers } from '../../common/interfaces';

export const addOrUnblockRFIDSuccessPayload = {
  cardNumber: '7777',
  cardUid: 'test',
  country: Countries.DE,
  userId: '12345',
};

export const address = {
  addressCity: 'Test City',
  addressCountry: Countries.DE,
  addressLine: 'Test Address',
  addressPostcode: '12345',
};

export const blockRFIDSuccessPayload = {
  cardNumber: '7777',
  cardUid: 'test',
  country: Countries.DE,
  reasonForBlocking: 'Request to Block',
  userId: '12345',
};

export const mockGetUserInfoSuccessContent: UserInfo = {
  balance: 0,
  country: Countries.NL,
  entitlements: {
    chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
    partnerSchemes: [],
    paymentMethods: ['PAYG-WALLET'],
    rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
    rfidEnabled: true,
    subsEnabled: true,
  },
  partnerType: SupportedPartners.UBER,
  revenuePlanNames: ['5066', 'HEAVY'],
  revenuePlans: [
    {
      provider: Providers.HASTOBE,
      revenuePlanDescription: 'Great description',
      revenuePlanName: 'HTB Uber',
    },
  ],
  roamingEnabled: true,
  tagIds: [
    {
      tagCardNumber: '7777',
      tagCategoryName: 'HASTOBE',
      tagId: 'tag-43',
      tagInternalId: 43,
      tagNotes: TagNotes.VIRTUAL_HTB,
      tagStatus: TagStatus.ACTIVE,
      tagTypeName: 'virtual',
    },
    {
      tagCardNumber: '7777',
      tagCategoryName: 'DCS',
      tagId: 'tag-44',
      tagInternalId: 44,
      tagNotes: TagNotes.VIRTUAL_DCS,
      tagStatus: TagStatus.ACTIVE,
      tagTypeName: 'virtual',
    },
  ],
  type: UserTypes.PAYG_Wallet,
  userStatus: UserStatus.ACTIVE,
};

export const requestFailedRFIDPayload = {
  address: {
    addressCity: 'dummy',
    addressCountry: Countries.DE,
    addressLine: 'dummy',
    addressPostcode: 'dummy',
  },
  cardPreference: 'Visa',
  country: Countries.DE,
  userId: null,
};

export const requestSuccessRFIDPayloadNL = {
  address: {
    addressCity: 'dummy',
    addressCountry: Countries.NL,
    addressLine: 'dummy',
    addressPostcode: 'dummy',
  },
  cardPreference: 'Visa',
  country: Countries.NL,
  userId: 'dummyuserID',
};

export const requestSuccessRFIDPayloadUK = {
  address: {
    addressCity: 'dummy',
    addressCountry: Countries.UK,
    addressLine: 'dummy',
    addressPostcode: 'dummy',
  },
  cardPreference: 'Visa',
  country: Countries.UK,
  type: 'bpcm',
  userId: 'dummyuserID',
};

export const userInfoPayload = {
  balance: 0,
  country: Countries.DE,
  entitlements: {
    chargepointsAvailable: ['DE-DCS', 'DE-HTB'],
    paymentMethods: ['PAYG-WALLET'],
    rfidDefaultProviders: [Providers.HASTOBE, Providers.BPCM],
    rfidEnabled: true,
  },
  partnerType: SupportedPartners.UBER,
  revenuePlans: [
    {
      provider: Providers.HASTOBE,
      revenuePlanDescription: 'Great description',
      revenuePlanName: 'HTB Uber',
    },
  ],
  roamingEnabled: true,
  tagIds: [
    {
      tagCardNumber: 'HASTOBE-card-number',
      tagCategoryName: 'HASTOBE',
      tagId: 'tag-43',
      tagNotes: TagNotes.VIRTUAL_HTB,
      tagStatus: TagStatus.ACTIVE,
      tagTypeName: 'virtual',
    },
    {
      tagCardNumber: 'dcs-card-number',
      tagCategoryName: 'DCS',
      tagId: 'tag-44',
      tagNotes: TagNotes.VIRTUAL_DCS,
      tagStatus: TagStatus.ACTIVE,
      tagTypeName: 'virtual',
    },
  ],
  type: UserTypes.PAYG_Wallet,
  userId: '12345',
};
