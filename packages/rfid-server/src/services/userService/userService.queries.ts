import { gql } from 'graphql-request';

export const TAG_INFO = gql`
  query tagInfo($tagCardNumber: String!, $country: String!) {
    tagInfo(tagCardNumber: $tagCardNumber, country: $country) {
      tagId
      tagStatus
      tagCardNumber
    }
  }
`;

// Query definiton for getting cached charge session data by user ID from Charge micro-service
export const USER_INFO = gql`
  query userInfo($userId: String!, $appCountry: String) {
    userInfo(userId: $userId, appCountry: $appCountry) {
      balance
      country
      entitlements {
        chargepointsAvailable
        paymentMethods
        rfidDefaultProviders
        rfidEnabled
      }
      partnerType
      revenuePlanNames
      revenuePlans {
        provider
        revenuePlanDescription
        revenuePlanName
      }
      roamingEnabled
      tagIds {
        tagCardNumber
        tagCategoryName
        tagId
        tagInternalId
        tagNotes
        tagStatus
        tagTypeName
      }
      type
      userStatus
    }
  }
`;

export const UPDATE_TAG_INTERNAL = gql`
  mutation updateTagInternal($payload: UpdateTagInternalInput!) {
    updateTagInternal(payload: $payload) {
      status
    }
  }
`;

export const ADD_ROAMING = gql`
  mutation addRoaming($country: String!, $userId: String!) {
    addRoaming(country: $country, userId: $userId) {
      status
      message
      dcsContractId
    }
  }
`;

export const UPSERT_RFID_TAG_PROVIDER_INTERNAL = gql`
  mutation upsertRFIDTagProviderInternal(
    $payload: upsertRFIDTagProviderInternalInput!
  ) {
    upsertRFIDTagProviderInternal(payload: $payload) {
      status
      message
    }
  }
`;
