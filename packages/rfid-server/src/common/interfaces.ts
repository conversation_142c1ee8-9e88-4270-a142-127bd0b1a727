import type { ItemList, Key } from 'aws-sdk/clients/dynamodb';
import type { Application } from 'express';
import type { Server } from 'http';
import type { Logger } from 'winston';

import {
  Countries,
  SupportedPartners,
  TagStatus,
  UserStatus,
  UserTypes,
} from './enums';

export interface RfidServerContext {
  userId: string;
  logTraceId: string;
  roles: string[];
  jaegersession?: string;
  jaegerrequest?: string;
}

export interface ConnectionContext {
  context: RfidServerContext;
}

export interface RequestHeaders {
  'x-apollo-user-id'?: string;
  'x-log-trace-id'?: string;
  'x-apollo-roles'?: string;
  jaegersession?: string;
  jaegerrequest?: string;
}

export interface RequestObject {
  headers: RequestHeaders;
}

export interface ApolloContextParams {
  connection?: ConnectionContext;
  req?: RequestObject;
}

export type ExpressAppWithServer = Application & {
  server: Server;
};

export interface RfidQueryParameters {
  TableName: string;
  IndexName?: string;
  KeyConditionExpression: string;
  ScanIndexForward: boolean;
  ExpressionAttributeValues: Record<string, string | undefined>;
  ExclusiveStartKey?: Key;
  Limit?: number;
}
export type ChargeProvidersType = 'DCS' | 'HTB' | 'CV-PAYG';

export interface RecursiveTagNumberGeneratorInput {
  country: Countries;
  provider: ChargeProvidersType;
  length: number;
  retries: number;
  logTraceId: string;
  logger: Logger;
}

export interface MergeTagNumberInput {
  country: Countries;
  provider: ChargeProvidersType;
  generatedId: string;
}

export interface IAddNewToken {
  uid: string;
  authId: string;
  type: string;
  ocpiIdentifier: string;
}

export interface IUpdateToken {
  uid: string;
  ocpiIdentifier: string;
  valid?: boolean;
}

export interface GetRfidParamsObjectStatus {
  countryCode?: string;
  status: string;
  exclusiveStartKey?: Key;
  limit?: number;
}

export interface GetRfidParamsObjectCardNumber {
  cardNumber: string;
  exclusiveStartKey: Key;
}

export interface UpdateRfidParamsObject {
  cardNumber: string;
  status: string;
  requestIdentifier: string;
}

export interface DeleteRfidParamsObject {
  cardNumber: string;
}

export interface RfidProviderFunctions {
  requestRFID: (
    payload: RequestRFID,
    userInfo: UserInfo | null,
    logTraceId: string,
  ) => Promise<RequestRFIDResponse | Error>;
  addRFID: (
    payload: AddRfid,
    logTraceId: string,
  ) => Promise<DefaultRfidResponse | Error>;
  replaceRFID: (
    userId: string,
    logTraceId: string,
  ) => Promise<ReplaceRfidResponse>;
  unblockRFID: (
    payload: UnblockRfid,
    logTraceId: string,
  ) => Promise<DefaultRfidResponse | Error>;
  blockRFID: (
    payload: BlockRfid,
    logTraceId: string,
  ) => Promise<DefaultRfidResponse | Error>;
}

export interface RfidProviderMapping {
  [issuer: string]: RfidProviderFunctions;
}

export interface CountryToServiceMap {
  [appCountry: string]: string;
}

export interface CountryConfigMap {
  [countryCode: string]: {
    companyCode: string;
    fleetGroup: string;
    physicalAuthMediaHTBEntityId: string;
  };
}

export interface RevenuePlan {
  revenuePlanName: string;
  revenuePlanDescription: string;
  provider: string;
}

export interface InsertItem {
  country?: string;
  first_name?: string;
  last_name?: string;
  date_added?: string;
  user_id?: string;
  rfid_card_number: string;
  address_line?: string;
  address_country?: string;
  address_postcode?: string;
  address_city?: string;
  additional_address_1?: string;
  additional_address_2?: string;
  rfid_status: string;
  partner_type: string;
}

export interface GetRfidResponse {
  country: Countries;
  first_name: string;
  last_name: string;
  date_added: string;
  user_id: string;
  rfid_card_number: string;
  address_line: string;
  address_country: Countries;
  address_postcode: string;
  address_city: string;
  additional_address_1: string;
  additional_address_2: string;
  rfid_status: string;
}

export interface DefaultRfidResponse {
  status: number;
  message: string;
}

export interface DefaultDynamoRfidResponse {
  data: ItemList;
}

export interface AddRfid {
  userId: string;
  cardUid: string;
  country: Countries;
  cardNumber: string;
}

export interface UnblockRfid {
  userId: string;
  country: Countries;
  cardUid: string;
  cardNumber: string;
}

export interface BlockRfid {
  userId: string;
  country: Countries;
  reasonForBlocking: string;
  cardUid: string;
  cardNumber: string;
}

interface ReplaceRfidResponseData {
  error: string | null;
  code: number | null;
  eventDetails: unknown;
  eventTime: unknown;
  message: string;
  salesforceId: string;
}

export interface ReplaceRfidResponse {
  status: number;
  data?: ReplaceRfidResponseData;
  message?: string;
}

export interface UpdateTagInternalInput {
  country: Countries;
  salesforceId: string;
  tagCardNumber: string;
  logTraceId?: string;
  tagStatus?: string;
  tagBarredDatetime?: string;
  tagExpiresDatetime?: string;
  tagSerialNumber?: string;
  tagNotes?: string;
  tagLastUsedDatetime?: string;
  tagDeletedFlag?: string;
  tagTypeName?: string;
  tagCategoryName?: string;
}

export interface RequestRFIDResponseData {
  eventDetails: string;
  eventTime: string;
  salesforceID: string;
}

export interface RequestRFIDResponse {
  status: number;
  data?: RequestRFIDResponseData;
}

export interface RequestRFIDAddressDetails {
  addressLine: string;
  addressCity: string;
  addressPostcode: string;
  addressCountry: Countries;
}

export interface RequestRFID {
  userId: string;
  country: Countries;
  address: RequestRFIDAddressDetails;
  cardPreference?: string;
  firstName?: string;
  lastName?: string;
}

export interface TagId {
  tagInternalId: number;
  tagId: string | null;
  tagCategoryName: string;
  tagTypeName: string;
  tagNotes: string;
  tagStatus: TagStatus;
  tagCardNumber: string;
}

interface OcpiTokenResponse {
  statusCode: number;
  statuisMessage: string;
  timestamp: string;
}

export interface UpdateTokenResponse {
  updateToken: OcpiTokenResponse;
}

export interface AddNewTokenResponse {
  addNewToken: OcpiTokenResponse;
}

export interface UpdateTokenInput {
  ocpiIdentifier: string;
  uid: string;
  valid?: boolean;
}

export interface RevenuePlanPayload {
  provider: string;
  revenuePlanName: string;
}

export interface UpdateRFIDInternalPayload {
  userId: string;
  country: Countries;
  revenuePlan: RevenuePlanPayload[];
}

export interface GetLatestReqIdentifierResponse {
  latestRequestIdentifier?: string;
  status?: number;
  message?: string;
}

export interface Entitlements {
  originEntitlementID?: string;
  rfidEnabled: boolean;
  rfidDefaultProviders: string[];
  subsEnabled: boolean;
  chargepointsAvailable: string[];
  paymentMethods: string[];
  partnerSchemes: string[];
}

export interface UserInfo {
  balance: number;
  country: Countries;
  entitlements: Entitlements;
  partnerType: SupportedPartners;
  revenuePlans: RevenuePlan[];
  revenuePlanNames?: string[];
  roamingEnabled?: boolean;
  tagIds: TagId[];
  type: UserTypes;
  userStatus: UserStatus;
}

export enum TagProviderStatus {
  ACTIVE = 'ACTIVE',
  BLOCKED = 'BLOCKED',
  TERMINATED = 'TERMINATED',
}

export interface upsertRFIDTagProviderInternalInput {
  tagId: number;
  tagProviderStatus: TagProviderStatus;
  providers: Providers[];
}

export interface UpdateInternalResponse {
  status: string;
  message: string;
}

export enum Providers {
  // HAVE_TODO: Remove HTB condition after charger migration is complete
  HTB = 'HTB',
  BPCM = 'BPCM',
  HASTOBE = 'HASTOBE',
  DCS = 'DCS',
  CHARGEVISION = 'CHARGEVISION',
  USOCPI = 'USOCPI',
}
