import {
  authenticate,
  authorisationLookup,
  getToken,
} from '../../services/cardinal/cardinal';
import { BpPayServerContext } from '../../types';
import {
  MutationAuthenticate3DsArgs,
  MutationAuthLookup3DsArgs,
  MutationGetToken3DsArgs,
} from '../../types/graphql';

const cardinalResolver = {
  Mutation: {
    getToken3DS: (
      _: undefined,
      args: MutationGetToken3DsArgs,
      ctx: BpPayServerContext,
    ) => getToken(ctx, args),
    authLookup3DS: async (
      _: undefined,
      args: MutationAuthLookup3DsArgs,
      ctx: BpPayServerContext,
    ) => authorisationLookup(ctx, args),
    authenticate3DS: async (
      _: undefined,
      args: MutationAuthenticate3DsArgs,
      ctx: BpPayServerContext,
    ) => authenticate(ctx, args),
  },
};

exports.resolver = cardinalResolver;
