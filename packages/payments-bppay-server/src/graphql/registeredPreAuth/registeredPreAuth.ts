import { v4 as uuidv4 } from 'uuid';

import { OrderStatus, PaymentService, PaymentType } from '../../enums';
import appTenantConfig from '../../services/bpPay/appTenantConfig';
import {
  AuthoriseWalletPaymentResponse,
  CreateOrderResponse,
  Payment,
} from '../../services/bpPay/interfaces';
import {
  authoriseWalletPayment,
  createOrder,
  getThreeDSData,
  getUserInfo,
  storeAuthOrder,
  updateOrStoreOrder,
} from '../../services/services';
import { PaymentAuthType } from '../../services/user/interfaces';
import { BpPayServerContext } from '../../types/apollo';
import { MutationRegisteredPreAuthArgs } from '../../types/graphql';
import dateNow from '../../utils/dateNow';
import formatUTCDate from '../../utils/formatUTCDate';
import { voidOrderService } from '../voidOrder/voidOrderService';

const serviceName = PaymentService.RegisteredPreAuthResolver;

export default async (
  ctx: BpPayServerContext,
  args: MutationRegisteredPreAuthArgs,
) => {
  const { appCountry, paymentId, paymentMethodId, threeDS, userId } = args;

  const userInfo = await getUserInfo(userId);
  if (userInfo.paymentAuthType === PaymentAuthType.MIT) {
    ctx.logger.info(`Pre-auth skipped: not required for this user.`, {
      serviceName,
      serviceApiResponseCode: 422,
    });

    return {
      status: '422',
      message: 'Pre-auth skipped: not required for this user.',
    };
  }

  // 1 PREPARE VARIABLES (and app tenant config)
  const {
    merchantId = '',
    preAuthAmount,
    preAuthCurrency,
    tenantConfig,
  } = appTenantConfig({
    paymentType: PaymentType.WALLET,
    appCountry: appCountry,
  });

  const country = appCountry?.toUpperCase();
  const correlationId = uuidv4();
  const operationUId = uuidv4();
  const currentTime = dateNow();
  const txnReferenceNumber = `000CG${formatUTCDate(dateNow())}`;

  // 2 CREATE ORDER
  const {
    status: createOrderStatus,
  }: CreateOrderResponse | { status: string } = await createOrder(ctx, {
    paymentId,
    merchantId: merchantId,
    country,
    correlationId,
    tenant: tenantConfig,
  }).catch((err: Error) => {
    ctx.logger.error('createOrder error', {
      serviceName,
      serviceApiResponseCode: 500,
      err,
    });
    return {
      status: OrderStatus.ERROR,
    };
  });

  // 2.1 Return 400 if order couldn't be created
  if (createOrderStatus !== OrderStatus.INITIALIZED) {
    ctx.logger.error('Preauth not initiated. Order could not be initialised.', {
      serviceName,
      serviceApiResponseCode: 400,
    });
    return {
      status: '400',
      message: 'Preauth not initiated. Order could not be initialised.',
    };
  }

  // 2.2 Store order into payments dynamoDB
  await updateOrStoreOrder(ctx, {
    paymentId,
    userId,
    startDate: Date.now(),
    orderStatus: createOrderStatus,
    merchantId,
    correlationId,
  }).catch((err: Error) => {
    ctx.logger.error('storeOrder error', {
      serviceName,
      serviceApiResponseCode: 500,
      err,
    });
    return { status: 500 };
  });

  // 3 AUTHORISE WALLET PAYMENT
  const {
    status: authWalletPaymentStatus,
    payment,
  }:
    | AuthoriseWalletPaymentResponse
    | { payment?: Payment; status: OrderStatus } = await authoriseWalletPayment(
    ctx,
    {
      paymentId: paymentId,
      operationUId,
      amount: preAuthAmount,
      currency: preAuthCurrency,
      timestamp: currentTime,
      paymentMethodId: paymentMethodId,
      userId: userId,
      threeDS: getThreeDSData(threeDS || undefined),
      country,
      tenant: tenantConfig,
      txnReferenceNumber,
      correlationId,
    },
    ctx.scope ?? undefined,
  ).catch((err: Error) => {
    ctx.logger.error('authoriseWalletPayment error', {
      serviceName,
      serviceApiResponseCode: 500,
      err,
    });
    return {
      status: OrderStatus.ERROR,
    };
  });

  // 3.1 Return 400 if payment couldn't be authorised
  if (authWalletPaymentStatus !== OrderStatus.AUTHORIZED) {
    ctx.logger.error(
      `Preauth not initiated. Payment could not be authorised.`,
      {
        serviceName,
        serviceApiResponseCode: 400,
        paymentId,
      },
    );

    return {
      status: '400',
      message: 'Preauth not initiated. Payment could not be authorised.',
    };
  }

  if (payment?.authorize?.amount === undefined) {
    ctx.logger.error(`Payment amount is unavailable`, {
      serviceName,
      serviceApiResponseCode: 400,
      paymentId,
    });

    return {
      status: '400',
      message: 'registeredPreAuth error: payment amount is unavailable',
    };
  }

  const voidOrderArgs = {
    paymentData: [{ paymentId, country: appCountry }],
  };

  if (payment.authorize.amount < preAuthAmount) {
    await voidOrderService(ctx, voidOrderArgs);

    ctx.logger.error(`Amount returned is less than the amount requested.`, {
      serviceName,
      serviceApiResponseCode: 400,
      paymentId,
    });

    return {
      status: '400',
      message:
        'registeredPreAuth error: amount returned is less than the amount requested',
    };
  }

  // 3.2 Store authorised order to payments dynamoDB
  const result = await storeAuthOrder(ctx, {
    paymentId: paymentId ?? '',
    userId: userId ?? '',
    amount: preAuthAmount,
    currency: preAuthCurrency,
    orderStatus: OrderStatus.AUTHORIZED,
    appCountry,
  }).catch((err: Error) => {
    ctx.logger.error('storePaymentStatus error', {
      serviceName,
      serviceApiResponseCode: 500,
      err,
    });
    return { status: 400 };
  });

  if (result.status !== 400) {
    ctx.logger.info('Mutation successful. Pre-Auth has been initialised', {
      serviceName,
      serviceApiResponseCode: 200,
      response: result,
      paymentId,
    });
    return { status: '200', message: 'Preauth initiated.' };
  } else {
    return { status: '400', message: 'Preauth not initiated.' };
  }
};
