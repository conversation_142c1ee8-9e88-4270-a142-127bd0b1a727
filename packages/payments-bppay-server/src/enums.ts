import { AuthenticationIndicatorType } from './types/graphql';

export enum PaymentMethodType {
  Mastercard = 'card',
  PayPal = 'paypal',
}

export enum DataStatus {
  Authorised = 'Authorised',
  Success = 'Success',
  Voided = 'Voided',
}

export enum Currency {
  EUR = 'EUR',
  GBP = 'GBP',
  USD = 'USD',
}

export enum Countries {
  UK = 'UK',
  ES = 'ES',
  NL = 'NL',
  DE = 'DE',
  GB = 'GB',
}

export enum CurrencyCode {
  UK = 'GBP',
  GB = 'GBP',
  NL = 'EUR',
  DE = 'EUR',
  ES = 'EUR',
}

export enum OrderStatus {
  INITIALIZED = 'initialized',
  AUTHORIZED = 'authorized',
  CAPTURED = 'captured',
  OUTSTANDING = 'outstanding',
  CAPTURED_ELSEWHERE = 'capturedElsewhere',
  VOIDED = 'voided',
  REFUNDED = 'refunded',
  REFUND_FAILED = 'refund_failed',
  ERROR = 'error',
  PROCESSING = 'processing',
}

export enum PaymentStatus {
  INITIALIZED = 'Initialized',
  AUTHORIZED = 'Authorized',
  CAPTURED = 'Captured',
  VOIDED = 'Voided',
  OUTSTANDING = 'Outstanding',
  REFUNDED = 'Refunded',
  REFUND_FAILED = 'Refund_failed',
  PROCESSING = 'Processing',
}

export enum PaymentDBStatus {
  CAPTURED = 'captured',
  CAPTURE_FAILED = 'CaptureFailed',
  OUTSTANDING = 'Outstanding',
  PROCESSING = 'processing',
}

export enum TransactionType {
  ALL = 'ALL',
  GUEST = 'GUEST',
  REGISTERED = 'REGISTERED',
  CONTACTLESS = 'CONTACTLESS',
  RFID = 'RFID',
}

export enum TransactionTypePrefix {
  REGISTERED = 'AM',
  GUEST = 'AW',
  RFID = 'AR',
  CONTACTLESS = 'AC',
}

export enum PaymentType {
  SIMPLE_PAYMENT = 'simplePayment',
  WALLET = 'wallet',
}

export enum AppType {
  WEB = 'web',
  MOBILE = 'mobile',
}

export enum UserType {
  GUEST = 'Guest',
  REGISTERED = 'Registered',
  SUBS = 'SUBS',
}

// Determine whether step up challenge is required from the user or not
export enum ChallengeIndicator {
  CHALLENGE_REQUESTED = '03',
  MANDATORY_CHALLENGE = '04',
}

// Category of the message for a specific use case
export enum MessageCategory {
  PA = '01',
  NPA = '02',
}

// Indicates the type of Authentication request
export enum AuthenticationIndicator {
  PAYMENT_TRANSACTION = '01',
  ADD_CARD = '04',
}

export enum ThreeDSUrl {
  AUTH_LOOKUP = 'consumerauth/lookup',
  AUTHENTICATE = 'consumerauth/authenticate',
  TOKEN = 'token',
}

export enum DateType {
  SALES_POSTING = 'SALES_POSTING',
  SALES_POSTING_MISSING = 'SALES_POSTING_MISSING',
  ORDER_STARTED = 'ORDER_STARTED',
}

export enum OperationType {
  Request = 'request',
  Response = 'response',
  Callback = 'callback',
  None = 'no-op specified',
}

export enum PaymentService {
  DPaaS = 'DPaaS',
  AuthoriseWalletPaymentResolver = 'authoriseWalletPaymentResolver',
  CapturePaymentResolver = 'capturePaymentResolver',
  CreateOrderResolver = 'createOrderResolver',
  RegisteredPreAuthResolver = 'registeredPreAuthResolver',
  CreatePaymentRecordInternalResolver = 'createPaymentRecordInternalResolver',
  MakeWalletMITPaymentResolver = 'makeWalletMITPaymentResolver',
  PreAuthResolver = 'preAuthResolver',
  SaveTransactionIdInternalResolver = 'saveTransactionIdInternalResolver',
  StartJourneyResolver = 'startJourneyResolver',
  StoreOperationIdResolver = 'storeOperationIdResolver',
  VoidOrderResolver = 'voidOrderResolver',
  RefundOrderResolver = 'refundOrderResolver',
  GetActivePreAuth = 'getActivePreAuthResolver',
}

export enum PaymentAction {
  AUTHORIZE = 'authorize',
  CREATE = 'create',
  CAPTURE = 'capture',
  VOID = 'void',
  REFUND = 'refund',
  PAY = 'pay',
  PRE_AUTH = 'preAuth',
  REGISTERED_PRE_AUTH = 'registeredPreAuth',
  START_JOURNEY = 'startJourney',
}

export enum PaymentServiceAPI {
  AUTH_LOOKUP = ThreeDSUrl.AUTH_LOOKUP,
  AUTHENTICATE = ThreeDSUrl.AUTHENTICATE,
  TOKEN = ThreeDSUrl.TOKEN,
  AUTHORIZE = PaymentAction.AUTHORIZE,
  CREATE = PaymentAction.CREATE,
  CAPTURE = PaymentAction.CAPTURE,
  VOID = PaymentAction.VOID,
  REFUND = PaymentAction.REFUND,
  PAY = PaymentAction.PAY,
  PRE_AUTH = PaymentAction.PRE_AUTH,
  REGISTERED_PRE_AUTH = PaymentAction.REGISTERED_PRE_AUTH,
  START_JOURNEY = PaymentAction.START_JOURNEY,
}

export const AuthenticatorIndicatorRequestMap: Record<
  AuthenticationIndicatorType,
  AuthenticationIndicator
> = {
  [AuthenticationIndicatorType.PREAUTH]:
    AuthenticationIndicator.PAYMENT_TRANSACTION,
  [AuthenticationIndicatorType.ADD_CARD]: AuthenticationIndicator.ADD_CARD,
};
