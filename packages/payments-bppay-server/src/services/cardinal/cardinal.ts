import { AxiosResponse } from 'axios';

import {
  AuthenticatorIndicatorRequestMap,
  ChallengeIndicator,
  Countries,
  CurrencyCode,
  MessageCategory,
  OperationType,
  PaymentService,
  PaymentServiceAPI,
  PaymentType,
  ThreeDSUrl,
} from '../../enums';
import AppTenantConfig from '../../services/bpPay/appTenantConfig';
import { BpPayServerContext } from '../../types';
import {
  AuthenticationIndicatorType,
  MutationAuthenticate3DsArgs,
  MutationAuthLookup3DsArgs,
  MutationGetToken3DsArgs,
} from '../../types/graphql';
import { callToDPaaS, getPaymentMethod } from './cardinal-helper';
import { AuthorisationLookupData, ChallengeParameters } from './interfaces';

export const getToken = async (
  ctx: BpPayServerContext,
  args: MutationGetToken3DsArgs,
): Promise<string> => {
  const requestPayload = {
    amount: args.amount,
    currencyCode: CurrencyCode[args.country as Countries],
    orderIdentifier: args.orderId,
  };

  try {
    const { data, status: serviceApiResponseCode } = (await callToDPaaS(
      ctx,
      args,
      requestPayload,
      ThreeDSUrl.TOKEN,
    )) as AxiosResponse<{ token: string; [key: string]: any }>;

    const { token, ...rest } = data;
    const tokenPreview = `${token.slice(0, 4)}…${token.slice(-4)}`;

    ctx.logger.info('getToken', {
      serviceName: PaymentService.DPaaS,
      serviceApi: PaymentServiceAPI.TOKEN,
      operationType: OperationType.Response,
      country: args.country,
      serviceApiResponseCode,
      correlationId: ctx.correlationId,
      requestPayload,
      dpaaSResponse: rest,
      tokenPreview,
    });

    return token;
  } catch (err: any) {
    const status = err?.response?.status;
    const data = err?.response?.data;

    ctx.logger.error(`getToken call to dpaas failed`, {
      serviceName: PaymentService.DPaaS,
      serviceApi: PaymentServiceAPI.TOKEN,
      operationType: OperationType.Response,
      serviceApiResponseCode: status,
      orderId: args.orderId,
      userId: args.userId,
      country: args.country,
      err,
      errorData: data,
      correlationId: ctx.correlationId,
    });

    throw err;
  }
};

export const authorisationLookup = async (
  ctx: BpPayServerContext,
  args: MutationAuthLookup3DsArgs,
): Promise<any> => {
  const paymentMethod = getPaymentMethod(args);
  if (
    args.authenticationIndicator &&
    args.authenticationIndicator !== AuthenticationIndicatorType.PREAUTH
  ) {
    const { preAuthAmount } = AppTenantConfig({
      paymentType: PaymentType.WALLET,
      appCountry: args.country,
    });
    if (preAuthAmount !== args.amount) {
      ctx.logger.info(
        `authorisationLookup: amount mismatch - expected ${preAuthAmount}, got ${args.amount}`,
        {
          serviceName: PaymentService.DPaaS,
          serviceApi: PaymentServiceAPI.AUTH_LOOKUP,
          operationType: OperationType.Request,
          country: args.country,
          correlationId: ctx.correlationId,
          userId: args.userId,
          requestPayload: args,
        },
      );
      throw new Error(
        `Amount mismatch: expected ${preAuthAmount}, got ${args.amount}`,
      );
    }
  }

  const provisioningParameters: ChallengeParameters = {
    amount:
      args.authenticationIndicator === AuthenticationIndicatorType.PREAUTH
        ? args.amount
        : 0,
    challengeIndicator: ChallengeIndicator.MANDATORY_CHALLENGE,
    messageCategory: MessageCategory.PA,
  };

  const authIndicator =
    args.authenticationIndicator ?? AuthenticationIndicatorType.ADD_CARD;
  const authenticationIndicator =
    AuthenticatorIndicatorRequestMap[authIndicator];

  const requestPayload: AuthorisationLookupData = {
    ...provisioningParameters,
    channel: 'sdk',
    currencyCode: CurrencyCode[args.country as Countries],
    dFReferenceId: args.referenceId,
    orderIdentifier: args.orderId,
    paymentMethod,
    authenticationIndicator,
  };

  try {
    const { data: raw, status: serviceApiResponseCode } = (await callToDPaaS(
      ctx,
      args,
      requestPayload,
      ThreeDSUrl.AUTH_LOOKUP,
    )) as AxiosResponse<any>;

    ctx.logger.info('authorisationLookup (full)', {
      serviceName: PaymentService.DPaaS,
      serviceApi: PaymentServiceAPI.AUTH_LOOKUP,
      operationType: OperationType.Response,
      country: args.country,
      serviceApiResponseCode,
      correlationId: ctx.correlationId,
      userId: args.userId,
      requestPayload,
      response: raw,
    });

    const core3DS = {
      eciFlag: raw.eciFlag,
      enrolled: raw.enrolled,
      cavv: raw.cavv,
      threeDSServerTransactionId: raw.threeDSServerTransactionId,
      paresStatus: raw.paresStatus,
      threeDSVersion: raw.threeDSVersion,
      statusReason: raw.statusReason ?? '',
      dsTransactionId: raw.dsTransactionId,
      acsTransactionId: raw.acsTransactionId,
      errorNumber: raw.errorNumber,
    };

    ctx.logger.info('authorisationLookup core fields', {
      serviceName: PaymentService.DPaaS,
      serviceApi: PaymentServiceAPI.AUTH_LOOKUP,
      operationType: OperationType.Response,
      country: args.country,
      serviceApiResponseCode,
      correlationId: ctx.correlationId,
      userId: args.userId,
      ...core3DS,
    });

    return raw;
  } catch (err: any) {
    const status = err?.response?.status;
    const data = err?.response?.data;

    ctx.logger.error('authorisationLookup failed', {
      serviceName: PaymentService.DPaaS,
      serviceApi: PaymentServiceAPI.AUTH_LOOKUP,
      operationType: OperationType.Response,
      serviceApiResponseCode: status,
      orderId: args.orderId,
      userId: args.userId,
      country: args.country,
      err,
      errorData: data,
      correlationId: ctx.correlationId,
    });

    throw err;
  }
};

export const authenticate = async (
  ctx: BpPayServerContext,
  args: MutationAuthenticate3DsArgs,
): Promise<any> => {
  const requestPayload = {
    transactionId: args.transactionId,
    paymentMethod: getPaymentMethod(args),
  };

  try {
    const { data, status: serviceApiResponseCode } = (await callToDPaaS(
      ctx,
      args,
      requestPayload,
      ThreeDSUrl.AUTHENTICATE,
    )) as AxiosResponse<any>;

    ctx.logger.info('authenticate', {
      serviceName: PaymentService.DPaaS,
      serviceApi: PaymentServiceAPI.AUTHENTICATE,
      operationType: OperationType.Response,
      country: args.country,
      serviceApiResponseCode,
      correlationId: ctx.correlationId,
      userId: args.userId,
      requestPayload,
      response: data,
    });

    return data;
  } catch (err: any) {
    const status = err?.response?.status;
    const data = err?.response?.data;

    ctx.logger.error('authenticate failed', {
      serviceName: PaymentService.DPaaS,
      serviceApi: PaymentServiceAPI.AUTHENTICATE,
      operationType: OperationType.Response,
      serviceApiResponseCode: status,
      transactionId: args.transactionId,
      userId: args.userId,
      country: args.country,
      err,
      errorData: data,
      correlationId: ctx.correlationId,
    });

    throw err;
  }
};
