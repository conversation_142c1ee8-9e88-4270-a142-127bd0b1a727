// This must be imported first so that the getDpaasAuthToken lib can consume the process.env
// eslint-disable-next-line simple-import-sort/imports
import env from '../../env';

import { getDPaaSAuthToken, TokenType } from '@bp/pulse-common';
import axios, { AxiosRequestConfig } from 'axios';
import { AppType, Countries, PaymentType, ThreeDSUrl } from '../../enums';
import { getNewCorrelationId } from '../../utils/helper';
import appTenantConfig from '../bpPay/appTenantConfig';
import { ThreeDSPaymentMethodType } from './interfaces';
import {
  MutationAuthenticate3DsArgs,
  MutationAuthLookup3DsArgs,
  MutationGetToken3DsArgs,
} from '../../types/graphql';
import { BpPayServerContext } from '../../types';

const dpaasCallType = (url: string) => {
  let result;
  switch (url) {
    case ThreeDSUrl.TOKEN:
      result = 'getToken';
      break;
    case ThreeDSUrl.AUTHENTICATE:
      result = 'authenticate';
      break;
    case ThreeDSUrl.AUTH_LOOKUP:
      result = 'authLookup';
      break;
    default:
      result = '';
  }
  return result;
};

type Auth3DsMutationArgs =
  | MutationGetToken3DsArgs
  | MutationAuthLookup3DsArgs
  | MutationAuthenticate3DsArgs;

export const getDPaaSHeaders = async (
  args: Auth3DsMutationArgs,
  correlationId: string,
): Promise<AxiosRequestConfig> => {
  const authToken = await getDPaaSAuthToken(TokenType.THREEDS);
  const { tenantConfig } = appTenantConfig({
    paymentType: PaymentType.WALLET,
    appType: AppType.MOBILE,
    appCountry: args.country,
  });
  const userId = args.userId || '';

  return {
    headers: {
      'x-tenant-country':
        args.country === Countries.UK ? Countries.GB : args.country,
      'x-tenant-id': tenantConfig,
      Authorization: `Bearer ${authToken}`,
      'x-correlation-id': correlationId || getNewCorrelationId(),
      'x-user-id': userId,
    },
  };
};

export const callToDPaaS = async (
  ctx: BpPayServerContext,
  args: Auth3DsMutationArgs,
  data: Record<string, unknown>,
  url: string,
) => {
  const headers = await getDPaaSHeaders(args, ctx.correlationId ?? '');

  ctx.logger.info(
    `3DS ${dpaasCallType(url)} to dpaas for userId ${
      args.userId
    } headers are ${JSON.stringify(headers)}`,
  );

  const cardinalUrl =
    env.CARDINAL_ENDPOINT ||
    'https://int-dpaas-3ds.digitalplatform.bp.com/b2c/3ds/dsp/payments/3ds/v1/us/cardinal';
  const urlPath = `${cardinalUrl}/${url}`;

  return axios.post(urlPath, data, headers);
};

export const getPaymentMethod = (
  args: MutationAuthLookup3DsArgs | MutationAuthenticate3DsArgs,
) => {
  return args.paymentMethodType === ThreeDSPaymentMethodType.ID
    ? {
        bpWallet: {
          paymentMethodId: args.paymentMethod,
          userId: args.userId,
        },
      }
    : {
        bpVault: {
          card: { token: args.paymentMethod },
        },
      };
};
