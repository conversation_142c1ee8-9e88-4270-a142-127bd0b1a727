import {
  AuthenticationIndicator,
  ChallengeIndicator,
  CurrencyCode,
  MessageCategory,
} from '../../enums';

export enum ThreeDSPaymentMethodType {
  TOKEN = 'TOKEN',
  ID = 'ID',
}

export interface IDpaasHeaders {
  headers: Record<string, unknown>;
}

export interface ChallengeParameters {
  amount: number;
  challengeIndicator: ChallengeIndicator;
  messageCategory: MessageCategory;
}

export interface AuthorisationLookupData extends ChallengeParameters {
  channel: 'sdk';
  currencyCode: CurrencyCode;
  dFReferenceId: string;
  orderIdentifier: string;
  paymentMethod: any;
  authenticationIndicator: AuthenticationIndicator;
  [key: string]: unknown;
}
export interface IThreeDS {
  eciFlag: string;
  enrolled: string;
  cavv: string;
  threeDSServerTransactionId: string;
  paresStatus: string;
  threeDSVersion: string;
  statusReason: string;
  dsTransactionId?: string;
  acsTransactionId?: string;
  errorNumber?: string;
}

export interface RawCardinal3DS {
  authenticate3DS(): IThreeDS;
}
