type AuthLookup {
  eciFlag: String
  enrolled: String
  errorDescription: String
  errorNumber: String
  orderId: String
  transactionId: String
  threeDSVersion: String
  signatureVerification: String
  cardBrand: String
  dsTransactionId: String
  acsUrl: String
  cavv: String
  paresStatus: String
  payload: String
  authenticationType: String
  challengeRequired: String
  threeDSServerTransactionId: String
  sdkFlowType: String
  acsTransactionId: String
}

enum AuthenticationIndicatorType {
  PREAUTH
  ADD_CARD
}

type Mutation {
  authLookup3DS(
    amount: Float!
    country: String!
    orderId: String!
    referenceId: String!
    paymentMethod: String!
    paymentMethodType: String!
    userId: String!
    authenticationIndicator: AuthenticationIndicatorType
  ): AuthLookup
}
