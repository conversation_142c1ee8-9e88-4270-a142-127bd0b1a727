/* eslint-disable */
import { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
};

export type AllSubscriptionsInput = {
  endDate: Scalars['String']['input'];
  startDate: Scalars['String']['input'];
};

export type ApplySubscription = {
  __typename?: 'ApplySubscription';
  subscription_code?: Maybe<Scalars['String']['output']>;
};

export type BillingAmount = {
  __typename?: 'BillingAmount';
  amount?: Maybe<Scalars['Float']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
};

export enum BillingCycle {
  MONTHLY = 'MONTHLY'
}

export type CancelSubscriptionResponse = {
  __typename?: 'CancelSubscriptionResponse';
  cancelledOn?: Maybe<Scalars['String']['output']>;
  endOfCurrentBillingCycle?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  membershipStatus?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  statusReason?: Maybe<Scalars['String']['output']>;
};

export type CardDetailsSubscription = {
  __typename?: 'CardDetailsSubscription';
  cardNumber?: Maybe<Scalars['String']['output']>;
  cardScheme?: Maybe<Scalars['String']['output']>;
};

export type CreateWalletSubscriptionResponse = {
  __typename?: 'CreateWalletSubscriptionResponse';
  error?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
};

export type GetAllSubsTransactionsInput = {
  /** Defaults to PAYMENT_CONFIRMED. */
  dateType?: InputMaybe<SubsDateType>;
  /**
   * Pass in ISO type date.
   * 1. ISO string: 2024-08-01T00:00:00.000Z
   *
   * End date can be the same day as start date.
   * End date cannot be before start date.
   * End date and start date have to be in the same month.
   */
  endDate: Scalars['String']['input'];
  lastEvaluatedKey?: InputMaybe<LastEvaluatedKeySubscriptionTransaction>;
  /**
   * Limit if you want to limit the number of results returned.
   * Defaults to no limit.
   */
  limit?: InputMaybe<Scalars['Int']['input']>;
  /**
   * If not provided, all payment statuses will be returned.
   * If provided as null all status will be returned.
   */
  paymentStatus?: InputMaybe<PaymentStatusEnum>;
  /**
   * Pass in ISO type date.
   * 1. ISO string: 2024-08-01T00:00:00.000Z
   *
   * Start and end date have to be in the same month.
   */
  startDate: Scalars['String']['input'];
};

export type GetAllSubsTransactionsResponse = {
  __typename?: 'GetAllSubsTransactionsResponse';
  error?: Maybe<Scalars['String']['output']>;
  /** Last evaluated key to be used in the next query if exists. */
  lastEvaluatedKey?: Maybe<LastEvaluatedKeySubscriptionTransactionUnion>;
  results?: Maybe<Array<Maybe<SubscriptionRecord>>>;
  status?: Maybe<Scalars['String']['output']>;
  totalResultsCount?: Maybe<Scalars['Int']['output']>;
};

export type GetSubscriptionRecordsResponse = {
  __typename?: 'GetSubscriptionRecordsResponse';
  lastKey?: Maybe<LastEvaluatedKeyType>;
  results?: Maybe<Array<Maybe<SubscriptionRecord>>>;
};

export type LastEvaluatedKeyDayIndexType = {
  __typename?: 'LastEvaluatedKeyDayIndexType';
  transaction_day?: Maybe<Scalars['String']['output']>;
  transaction_id?: Maybe<Scalars['String']['output']>;
};

export type LastEvaluatedKeyInput = {
  transaction_id?: InputMaybe<Scalars['String']['input']>;
};

export type LastEvaluatedKeyMonthIndexType = {
  __typename?: 'LastEvaluatedKeyMonthIndexType';
  transaction_id?: Maybe<Scalars['String']['output']>;
  transaction_month?: Maybe<Scalars['String']['output']>;
};

export type LastEvaluatedKeySubscriptionTransaction = {
  transaction_day?: InputMaybe<Scalars['String']['input']>;
  /**
   * Pass only combination of transaction_id and transaction_month
   * or transaction_id and transaction_day.
   *
   * Otherwise will throw an error.
   */
  transaction_id?: InputMaybe<Scalars['String']['input']>;
  transaction_month?: InputMaybe<Scalars['String']['input']>;
};

export type LastEvaluatedKeySubscriptionTransactionUnion = LastEvaluatedKeyDayIndexType | LastEvaluatedKeyMonthIndexType;

export type LastEvaluatedKeyType = {
  __typename?: 'LastEvaluatedKeyType';
  transaction_id?: Maybe<Scalars['String']['output']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  cancelWalletSubscription?: Maybe<CancelSubscriptionResponse>;
  createSubsHistoryRecord?: Maybe<CreateSubsHistoryRecordResponse>;
  createWalletSubsPlan?: Maybe<SubsPlansResponse>;
  createWalletSubscription?: Maybe<CreateWalletSubscriptionResponse>;
  retryWalletSubsPayment?: Maybe<SubsPaymentResponse>;
  updateSubsPlanDpaas?: Maybe<UpdatedSubsPlanResponse>;
  updateSubsSalesPostingDate?: Maybe<UpdateSubsSalesPostingDateResponse>;
  updateWalletSubscription?: Maybe<UpdateSubscriptionDetails>;
  upsertWalletSubscription?: Maybe<UpsertWalletSubscriptionResponse>;
};


export type MutationCancelWalletSubscriptionArgs = {
  userId: Scalars['String']['input'];
};


export type MutationCreateSubsHistoryRecordArgs = {
  paymentStatus: Scalars['String']['input'];
  retrievalReferenceNumber?: InputMaybe<Scalars['String']['input']>;
  subsHistoryRecord: SubsHistoryRecord;
  transactionId: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type MutationCreateWalletSubsPlanArgs = {
  payload?: InputMaybe<SubsPlan>;
};


export type MutationCreateWalletSubscriptionArgs = {
  country?: InputMaybe<Scalars['String']['input']>;
  offerCode?: InputMaybe<Scalars['String']['input']>;
  paymentMethodId: Scalars['String']['input'];
  threeDS?: InputMaybe<ThreeDs>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationRetryWalletSubsPaymentArgs = {
  country?: InputMaybe<Scalars['String']['input']>;
  membershipId?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateSubsPlanDpaasArgs = {
  payload?: InputMaybe<UpdateSubsPlanPayload>;
};


export type MutationUpdateSubsSalesPostingDateArgs = {
  salesPostingDate: Scalars['Float']['input'];
  transactionId: Scalars['String']['input'];
};


export type MutationUpdateWalletSubscriptionArgs = {
  offerSubsPlanId?: InputMaybe<Scalars['String']['input']>;
  paymentMethodId?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};


export type MutationUpsertWalletSubscriptionArgs = {
  offerCode?: InputMaybe<Scalars['String']['input']>;
  paymentMethodId?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['String']['input']>;
};

export enum PaymentStatusEnum {
  ALL = 'ALL',
  CAPTURED = 'CAPTURED',
  FAILED = 'FAILED'
}

export enum PlanType {
  BASE = 'BASE',
  OFFER = 'OFFER'
}

export type PriceDetailsSubscription = {
  __typename?: 'PriceDetailsSubscription';
  currency?: Maybe<Scalars['String']['output']>;
  defaultFee?: Maybe<Scalars['String']['output']>;
  priceBase?: Maybe<Scalars['String']['output']>;
  priceDiscount?: Maybe<Scalars['String']['output']>;
  priceGross?: Maybe<Scalars['String']['output']>;
  priceNet?: Maybe<Scalars['String']['output']>;
  priceTaxRate?: Maybe<Scalars['String']['output']>;
  priceVAT?: Maybe<Scalars['String']['output']>;
};

export type Query = {
  __typename?: 'Query';
  /**
   * Returns all transactions for a given month or day.
   * Results are paginated.
   */
  getAllSubsTransactions?: Maybe<GetAllSubsTransactionsResponse>;
  getSubsWalletHistoryRecord?: Maybe<SubscriptionRecord>;
  getSubsWalletHistoryRecords?: Maybe<GetSubscriptionRecordsResponse>;
  getWalletSubscription?: Maybe<SubscriptionDetails>;
};


export type QueryGetAllSubsTransactionsArgs = {
  input?: InputMaybe<GetAllSubsTransactionsInput>;
};


export type QueryGetSubsWalletHistoryRecordArgs = {
  subsTransactionID: Scalars['String']['input'];
  userId: Scalars['String']['input'];
};


export type QueryGetSubsWalletHistoryRecordsArgs = {
  lastKey?: InputMaybe<LastEvaluatedKeyInput>;
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  userId: Scalars['String']['input'];
};


export type QueryGetWalletSubscriptionArgs = {
  userId?: InputMaybe<Scalars['String']['input']>;
};

export type SubsCardDetails = {
  card_number?: InputMaybe<Scalars['String']['input']>;
  card_scheme?: InputMaybe<Scalars['String']['input']>;
};

export enum SubsDateType {
  PAYMENT_CONFIRMED = 'PAYMENT_CONFIRMED',
  SALES_POSTING = 'SALES_POSTING',
  SALES_POSTING_MISSING = 'SALES_POSTING_MISSING'
}

export type SubsHistoryRecord = {
  cardDetails?: InputMaybe<SubsCardDetails>;
  country?: InputMaybe<Scalars['String']['input']>;
  isTrial?: InputMaybe<Scalars['Boolean']['input']>;
  nextBillingCycleDate?: InputMaybe<Scalars['String']['input']>;
  partnerType?: InputMaybe<Scalars['String']['input']>;
  priceDetails?: InputMaybe<SubsPriceDetails>;
  salesPostingDate?: InputMaybe<Scalars['Float']['input']>;
  subscriptionExternalId?: InputMaybe<Scalars['String']['input']>;
  transactionDate?: InputMaybe<Scalars['String']['input']>;
};

export type SubsPaymentResponse = {
  __typename?: 'SubsPaymentResponse';
  accountId?: Maybe<Scalars['String']['output']>;
  cancelledOn?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  nextBillingDate?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  statusReason?: Maybe<Scalars['String']['output']>;
  useCase?: Maybe<Scalars['String']['output']>;
};

export type SubsPlan = {
  billingAmount: Scalars['Float']['input'];
  country?: InputMaybe<Scalars['String']['input']>;
  currency: Scalars['String']['input'];
  default?: InputMaybe<Scalars['Boolean']['input']>;
  description: Scalars['String']['input'];
  duration?: InputMaybe<Scalars['Int']['input']>;
  planName: Scalars['String']['input'];
  planType?: InputMaybe<PlanType>;
  scheme_id?: InputMaybe<Scalars['Int']['input']>;
};

export type SubsPlansResponse = {
  __typename?: 'SubsPlansResponse';
  error?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
  planName?: Maybe<Scalars['String']['output']>;
};

export type SubsPriceDetails = {
  currency?: InputMaybe<Scalars['String']['input']>;
  price_VAT?: InputMaybe<Scalars['String']['input']>;
  price_base?: InputMaybe<Scalars['String']['input']>;
  price_discount?: InputMaybe<Scalars['String']['input']>;
  price_full_gross?: InputMaybe<Scalars['String']['input']>;
  price_gross?: InputMaybe<Scalars['String']['input']>;
  price_net?: InputMaybe<Scalars['String']['input']>;
  price_tax?: InputMaybe<Scalars['String']['input']>;
};

export type SubscriptionDetails = {
  __typename?: 'SubscriptionDetails';
  accountId?: Maybe<Scalars['String']['output']>;
  agreementId?: Maybe<Scalars['String']['output']>;
  billingItems?: Maybe<Array<Maybe<Subscription_BillingItems>>>;
  cancelledOn?: Maybe<Scalars['String']['output']>;
  createdAt?: Maybe<Scalars['String']['output']>;
  customerId?: Maybe<Scalars['String']['output']>;
  deactivationDate?: Maybe<Scalars['String']['output']>;
  discountExpiresAt?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  merchantId?: Maybe<Scalars['String']['output']>;
  nextBillingDate?: Maybe<Scalars['String']['output']>;
  paymentMethodId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  statusReason?: Maybe<Scalars['String']['output']>;
  tenantId?: Maybe<Scalars['String']['output']>;
  useCase?: Maybe<Scalars['String']['output']>;
};

export type SubscriptionRecord = {
  __typename?: 'SubscriptionRecord';
  cardDetails?: Maybe<CardDetailsSubscription>;
  country?: Maybe<Scalars['String']['output']>;
  isTrial?: Maybe<Scalars['Boolean']['output']>;
  nextBillingCycleDate?: Maybe<Scalars['String']['output']>;
  partnerType?: Maybe<Scalars['String']['output']>;
  paymentStatus?: Maybe<PaymentStatusEnum>;
  priceDetails?: Maybe<PriceDetailsSubscription>;
  retrievalReferenceNumber?: Maybe<Scalars['String']['output']>;
  salesPostingDate?: Maybe<Scalars['Float']['output']>;
  subsAccountId?: Maybe<Scalars['String']['output']>;
  transactionDate?: Maybe<Scalars['String']['output']>;
  transactionDay?: Maybe<Scalars['String']['output']>;
  transactionId?: Maybe<Scalars['String']['output']>;
  transactionMonth?: Maybe<Scalars['String']['output']>;
  userId?: Maybe<Scalars['String']['output']>;
};

export type Subscription_BillingItems = {
  __typename?: 'Subscription_BillingItems';
  amount?: Maybe<Scalars['String']['output']>;
  billingCycleCount?: Maybe<Scalars['Int']['output']>;
  billingItemId?: Maybe<Scalars['String']['output']>;
  currency?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
};

export type ThreeDs = {
  acsTransactionId?: InputMaybe<Scalars['String']['input']>;
  cavv?: InputMaybe<Scalars['String']['input']>;
  dsTransactionId?: InputMaybe<Scalars['String']['input']>;
  eciFlag?: InputMaybe<Scalars['String']['input']>;
  enrolled?: InputMaybe<Scalars['String']['input']>;
  paresStatus?: InputMaybe<Scalars['String']['input']>;
  statusReason?: InputMaybe<Scalars['String']['input']>;
  threeDSServerTransactionId?: InputMaybe<Scalars['String']['input']>;
  threeDSVersion?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateSubsPlanPayload = {
  billingAmount?: InputMaybe<Scalars['Float']['input']>;
  country?: InputMaybe<Scalars['String']['input']>;
  currency?: InputMaybe<Scalars['String']['input']>;
  default: Scalars['Boolean']['input'];
  duration?: InputMaybe<Scalars['Int']['input']>;
  planName?: InputMaybe<Scalars['String']['input']>;
  subscriptionPlanId: Scalars['String']['input'];
};

export type UpdateSubsSalesPostingDateResponse = {
  __typename?: 'UpdateSubsSalesPostingDateResponse';
  message?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type UpdateSubscriptionDetails = {
  __typename?: 'UpdateSubscriptionDetails';
  accountId?: Maybe<Scalars['String']['output']>;
  agreementId?: Maybe<Scalars['String']['output']>;
  billingItems?: Maybe<Array<Maybe<UpdateSubscription_BillingItems>>>;
  createdAt?: Maybe<Scalars['String']['output']>;
  customerId?: Maybe<Scalars['String']['output']>;
  deactivationDate?: Maybe<Scalars['String']['output']>;
  discountExpiresAt?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  merchantId?: Maybe<Scalars['String']['output']>;
  nextBillingDate?: Maybe<Scalars['String']['output']>;
  paymentMethodId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  statusReason?: Maybe<Scalars['String']['output']>;
  tenantId?: Maybe<Scalars['String']['output']>;
  useCase?: Maybe<Scalars['String']['output']>;
};

export type UpdateSubscription_BillingItems = {
  __typename?: 'UpdateSubscription_BillingItems';
  billingItemId?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
};

export type UpdatedSubsPlanResponse = {
  __typename?: 'UpdatedSubsPlanResponse';
  accountId?: Maybe<Scalars['String']['output']>;
  billingAmount?: Maybe<BillingAmount>;
  billingCycle?: Maybe<BillingCycle>;
  billingCycleCount?: Maybe<Scalars['Int']['output']>;
  billingFrequency?: Maybe<Scalars['Int']['output']>;
  createdAt?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  planId?: Maybe<Scalars['String']['output']>;
  planName?: Maybe<Scalars['String']['output']>;
  planType?: Maybe<PlanType>;
  status?: Maybe<Scalars['Int']['output']>;
};

export type UpsertWalletSubscriptionResponse = {
  __typename?: 'UpsertWalletSubscriptionResponse';
  accountId?: Maybe<Scalars['String']['output']>;
  agreementId?: Maybe<Scalars['String']['output']>;
  billingItems?: Maybe<Array<Maybe<Subscription_BillingItems>>>;
  createdAt?: Maybe<Scalars['String']['output']>;
  customerId?: Maybe<Scalars['String']['output']>;
  deactivationDate?: Maybe<Scalars['String']['output']>;
  discountExpiresAt?: Maybe<Scalars['String']['output']>;
  error?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  merchantId?: Maybe<Scalars['String']['output']>;
  nextBillingDate?: Maybe<Scalars['String']['output']>;
  paymentMethodId?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  statusReason?: Maybe<Scalars['String']['output']>;
  subscriptionId?: Maybe<Scalars['String']['output']>;
  tenantId?: Maybe<Scalars['String']['output']>;
  useCase?: Maybe<Scalars['String']['output']>;
};

export type CreateSubsHistoryRecordResponse = {
  __typename?: 'createSubsHistoryRecordResponse';
  error?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  subscriptionExternalId?: Maybe<Scalars['String']['output']>;
  transactionId?: Maybe<Scalars['String']['output']>;
};

export type UpdateWalletSubscriptionMutationVariables = Exact<{
  userId: Scalars['String']['input'];
  paymentMethodId?: InputMaybe<Scalars['String']['input']>;
  offerSubsPlanId?: InputMaybe<Scalars['String']['input']>;
}>;


export type UpdateWalletSubscriptionMutation = { __typename?: 'Mutation', updateWalletSubscription?: { __typename?: 'UpdateSubscriptionDetails', id?: string | null, error?: string | null } | null };

export type WalletSubscriptionQueryVariables = Exact<{
  userId: Scalars['String']['input'];
}>;


export type WalletSubscriptionQuery = { __typename?: 'Query', getWalletSubscription?: { __typename?: 'SubscriptionDetails', id?: string | null, statusReason?: string | null, nextBillingDate?: string | null, createdAt?: string | null, error?: string | null, billingItems?: Array<{ __typename?: 'Subscription_BillingItems', billingCycleCount?: number | null, planId?: string | null } | null> | null } | null };


export const UpdateWalletSubscriptionDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"mutation","name":{"kind":"Name","value":"updateWalletSubscription"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"userId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"paymentMethodId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}},{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"offerSubsPlanId"}},"type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"updateWalletSubscription"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"userId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"userId"}}},{"kind":"Argument","name":{"kind":"Name","value":"paymentMethodId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"paymentMethodId"}}},{"kind":"Argument","name":{"kind":"Name","value":"offerSubsPlanId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"offerSubsPlanId"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"error"}}]}}]}}]} as unknown as DocumentNode<UpdateWalletSubscriptionMutation, UpdateWalletSubscriptionMutationVariables>;
export const WalletSubscriptionDocument = {"kind":"Document","definitions":[{"kind":"OperationDefinition","operation":"query","name":{"kind":"Name","value":"walletSubscription"},"variableDefinitions":[{"kind":"VariableDefinition","variable":{"kind":"Variable","name":{"kind":"Name","value":"userId"}},"type":{"kind":"NonNullType","type":{"kind":"NamedType","name":{"kind":"Name","value":"String"}}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"getWalletSubscription"},"arguments":[{"kind":"Argument","name":{"kind":"Name","value":"userId"},"value":{"kind":"Variable","name":{"kind":"Name","value":"userId"}}}],"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"id"}},{"kind":"Field","name":{"kind":"Name","value":"statusReason"}},{"kind":"Field","name":{"kind":"Name","value":"nextBillingDate"}},{"kind":"Field","name":{"kind":"Name","value":"createdAt"}},{"kind":"Field","name":{"kind":"Name","value":"billingItems"},"selectionSet":{"kind":"SelectionSet","selections":[{"kind":"Field","name":{"kind":"Name","value":"billingCycleCount"}},{"kind":"Field","name":{"kind":"Name","value":"planId"}}]}},{"kind":"Field","name":{"kind":"Name","value":"error"}}]}}]}}]} as unknown as DocumentNode<WalletSubscriptionQuery, WalletSubscriptionQueryVariables>;