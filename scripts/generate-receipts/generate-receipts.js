const axios = require('axios');
const fs = require('fs');

const filePath = process.argv.slice(2)[0];

if (!filePath) {
  console.error('Please provide path to the json file with receipts data');
  return;
}

const file = fs.readFileSync(filePath);
const list = JSON.parse(file);

const generateReceipts = async (data) => {
  for (item of data) {
    const buff = new Buffer.from(JSON.stringify(item));
    const base64data = buff.toString('base64');

    console.log('req data', base64data);
    await axios
      .put(
        'https://gmol2hcwhi.execute-api.eu-west-2.amazonaws.com/Chargemaster/Streams/ws-00f0-pdf-receipts-test/record',
        {
          Data: base64data,
        },
        {
          headers: {
            'x-api-key': 'POPULATE',
            'Content-Type': 'application/json',
          },
        },
      )
      .then((res) => {
        console.log('response', res.data);
      })
      .catch((e) => {
        console.log('error', e.response.data);
      });
  }
};

generateReceipts(list);
