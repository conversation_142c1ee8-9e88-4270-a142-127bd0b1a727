{"name": "chargePointAvailabilityCheckTrigger", "version": "1.0.0", "private": true, "description": "Lambda to trigger chargePointAvailabilityCheck across configured markets/providers", "license": "MIT", "type": "commonjs", "main": "dist/index.js", "scripts": {"build": "npm ci && tsc && cp package.json dist/package.json && cp package-lock.json dist/package-lock.json && cd dist && npm ci --production", "clean": "rm -rf dist/", "types:check": "tsc --noEmit"}, "dependencies": {"@aws-sdk/client-lambda": "^3.520.0"}, "devDependencies": {"@types/node": "^24.1.0", "typescript": "^5.3.3"}}