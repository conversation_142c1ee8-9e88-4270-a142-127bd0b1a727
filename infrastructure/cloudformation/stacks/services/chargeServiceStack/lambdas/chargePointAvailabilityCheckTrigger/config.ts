/**
 * config.ts
 * Reads and parses CHARGEPOINT_AVAILABILITY_CHECK_ENABLED_MARKETS from environment.
 */

export const getEnabledMarkets = (): string[] => {
  const rawEnabledMarkets =
    process.env.CHARGEPOINT_AVAILABILITY_CHECK_ENABLED_MARKETS;

  if (!rawEnabledMarkets) {
    console.warn(
      '[CONFIG] CHARGEPOINT_AVAILABILITY_CHECK_ENABLED_MARKETS is undefined or empty.',
    );
    return [];
  }

  try {
    // Parse the JSON string first
    const parsedMarkets = JSON.parse(rawEnabledMarkets);

    if (!Array.isArray(parsedMarkets)) {
      console.error(
        '[CONFIG] CHARGEPOINT_AVAILABILITY_CHECK_ENABLED_MARKETS must be a JSON array.',
      );
      return [];
    }

    return parsedMarkets;
  } catch (e) {
    console.error(
      '[CONFIG] Failed to parse CHARGEPOINT_AVAILABILITY_CHECK_ENABLED_MARKETS:',
      e,
    );
    return [];
  }
};
