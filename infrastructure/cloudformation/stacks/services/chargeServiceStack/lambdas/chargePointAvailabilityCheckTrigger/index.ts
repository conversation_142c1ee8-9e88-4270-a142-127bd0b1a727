import { LambdaClient, InvokeCommand } from '@aws-sdk/client-lambda';
import { getEnabledMarkets } from './config';
import { parseAndValidate } from './utils/parseInput';

const lambdaClient = new LambdaClient({});

export const handler = async (): Promise<void> => {
  const rawMarkets = getEnabledMarkets();

  if (!rawMarkets.length) {
    console.warn('[Trigger] No markets configured to run checks.');
    return;
  }

  console.log(
    `[Trigger] Target Lambda: ${process.env.CHARGEPOINT_AVAILABILITY_CHECK_LAMBDA_NAME}`,
  );
  console.log(
    `[Trigger] Processing ${rawMarkets.length} markets: ${rawMarkets.join(
      ', ',
    )}`,
  );

  let hasAnyFailure = false;

  for (const entry of rawMarkets) {
    const parsed = parseAndValidate(entry);
    if (!parsed) {
      console.warn(`[Trigger] ⚠️ Skipping invalid entry: ${entry}`);
      continue;
    }

    try {
      console.log(
        `[Trigger] 🚀 Invoking target <PERSON><PERSON> for ${entry} with payload:`,
        JSON.stringify(parsed),
      );

      const result = await lambdaClient.send(
        new InvokeCommand({
          FunctionName: process.env.CHARGEPOINT_AVAILABILITY_CHECK_LAMBDA_NAME!,
          InvocationType: 'Event',
          Payload: Buffer.from(JSON.stringify(parsed)),
        }),
      );

      console.info(
        `[Trigger] ✅ Successfully invoked for ${entry}. StatusCode: ${result.StatusCode}`,
      );

      // Log the response payload if there is one (though Event invocation usually doesn't return much)
      if (result.Payload) {
        const responsePayload = Buffer.from(result.Payload).toString();
        console.log(`[Trigger] Response payload: ${responsePayload}`);
      }
    } catch (error) {
      hasAnyFailure = true;
      console.error(`[Trigger] ❌ Failed to invoke for ${entry}:`, {
        error,
      });
    }
  }

  if (hasAnyFailure) {
    throw new Error(
      '[Trigger] One or more invocations failed. Failing the Lambda run.',
    );
  }

  console.log('[Trigger] 🎉 All invocations completed successfully');
};
