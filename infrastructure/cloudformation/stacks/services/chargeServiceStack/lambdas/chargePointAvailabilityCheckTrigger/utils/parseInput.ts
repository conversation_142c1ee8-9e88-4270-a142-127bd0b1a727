import { SupportedCountries, Providers } from '../enums';

type ValidInput = {
  market: SupportedCountries;
  provider: Providers;
};

// Allow aliases like 'CV' → 'CHARGEVISION', 'HTB' → 'hasToBe'
const providerAliasMap: Record<string, Providers> = {
  CV: Providers.CHARGEVISION,
  CHARGEVISION: Providers.CHARGEVISION,
  HTB: Providers.HTB,
  HASTOBE: Providers.HTB,
};

export const parseAndValidate = (entry: string): ValidInput | null => {
  const [marketRaw, providerAlias] = entry.split('-');

  const market = marketRaw.toUpperCase() as SupportedCountries;
  // Convert to uppercase to match the map keys
  const provider = providerAliasMap[providerAlias.toUpperCase()];

  if (!market || !Object.values(SupportedCountries).includes(market)) {
    console.warn(`[Parse] Invalid market: ${marketRaw}`);
    return null;
  }

  if (!provider) {
    console.warn(`[Parse] Unknown provider alias: ${providerAlias}`);
    return null;
  }

  return { market, provider };
};
