[{"ParameterKey": "Environment", "ParameterValue": "#{Environment}#"}, {"ParameterKey": "EnvironmentShortName", "ParameterValue": "#{EnvironmentShortName}#"}, {"ParameterKey": "ApiVersion", "ParameterValue": "#{ApiVersion}#"}, {"ParameterKey": "StackName", "ParameterValue": "#{StackName}#"}, {"ParameterKey": "BuildId", "ParameterValue": "#{BuildId}#"}, {"ParameterKey": "StackBucketName", "ParameterValue": "#{StackBucketName}#"}, {"ParameterKey": "AwsLogicalAccountNameUpperCase", "ParameterValue": "#{AwsLogicalAccountNameUpperCase}#"}, {"ParameterKey": "AwsLogicalAccountNameLowerCase", "ParameterValue": "#{AwsLogicalAccountNameLowerCase}#"}, {"ParameterKey": "LambdaSecurityGroup", "ParameterValue": "#{LambdaSecurityGroup}#"}, {"ParameterKey": "ExternalSubnetIds", "ParameterValue": "#{ExternalSubnetIds}#"}, {"ParameterKey": "RedisNodeType", "ParameterValue": "#{RedisNodeType}#"}, {"ParameterKey": "RedisCacheSubnetGroup", "ParameterValue": "#{RedisCacheSubnetGroup}#"}, {"ParameterKey": "ElastiCacheSecurityGroups", "ParameterValue": "#{ElastiCacheSecurityGroups}#"}, {"ParameterKey": "LambdaRoleArn", "ParameterValue": "#{LambdaRoleARN}#"}, {"ParameterKey": "ChargepointEventsKinesisStreamArn", "ParameterValue": "#{ChargepointEventsKinesisStreamArn}#"}, {"ParameterKey": "HtbChargepointEventsKinesisStreamArn", "ParameterValue": "#{HtbChargepointEventsKinesisStreamArn}#"}, {"ParameterKey": "HtbNotifyKinesisStreamArn", "ParameterValue": "#{HtbNotifyKinesisStreamArn}#"}, {"ParameterKey": "HtbCdrEventsKinesisStreamArn", "ParameterValue": "#{HtbCdrEventsKinesisStreamArn}#"}, {"ParameterKey": "FavouritesDbUrl", "ParameterValue": "#{FAVOURITES_DB_URL}#"}, {"ParameterKey": "PaymentsDbRegion", "ParameterValue": "#{PAYMENTS_DB_REGION}#"}, {"ParameterKey": "PaymentsDbTableName", "ParameterValue": "#{PAYMENTS_DB_TABLE_NAME}#"}, {"ParameterKey": "PrivateBppayServerHttp", "ParameterValue": "#{PRIVATE_BPPAY_SERVER_HTTP}#"}, {"ParameterKey": "InternalBppayServerHttp", "ParameterValue": "#{INTERNAL_BPPAY_SERVER_HTTP}#"}, {"ParameterKey": "ApiGatewayKey", "ParameterValue": "ApiGateway-ApiKey"}, {"ParameterKey": "ApiGatewayStreamKey", "ParameterValue": "ApiGatewayStream-ApiKey"}, {"ParameterKey": "AwsServicesKey", "ParameterValue": "AwsServices-ApiKey"}, {"ParameterKey": "InternalChargeServerHttp", "ParameterValue": "#{INTERNAL_CHARGE_SERVER_HTTP}#"}, {"ParameterKey": "InternalHistoryServerHttp", "ParameterValue": "#{INTERNAL_HISTORY_SERVER_HTTP}#"}, {"ParameterKey": "InternalVoucherServerHttp", "ParameterValue": "#{INTERNAL_VOUCHER_SERVER_HTTP}#"}, {"ParameterKey": "PdfReceiptGatewayStreamUrl", "ParameterValue": "#{PDF_RECEIPT_GATEWAY_STREAM_URL}#"}, {"ParameterKey": "PrivateGatewayServerHttp", "ParameterValue": "#{PRIVATE_GATEWAY_SERVER_HTTP}#"}, {"ParameterKey": "ApolloInternalSecret", "ParameterValue": "ApolloInternal-Secret"}, {"ParameterKey": "HtbServicesBearerToken", "ParameterValue": "HtbServices-BearerToken"}, {"ParameterKey": "HtbServicesPasswordDE", "ParameterValue": "HtbServicesDE-Password"}, {"ParameterKey": "HtbServicesPasswordES", "ParameterValue": "HtbServicesES-Password"}, {"ParameterKey": "HtbServicesPasswordNL", "ParameterValue": "HtbServicesNL-Password"}, {"ParameterKey": "HtbServicesPasswordES", "ParameterValue": "HtbServicesES-Password"}, {"ParameterKey": "HtbServicesPasswordUK", "ParameterValue": "HtbServicesUK-Password"}, {"ParameterKey": "HtbServicesRest", "ParameterValue": "#{HTB_SERVICES_REST}#"}, {"ParameterKey": "HtbServicesUrl", "ParameterValue": "#{HTB_SERVICES_URL}#"}, {"ParameterKey": "HtbServicesUsernameDE", "ParameterValue": "#{HTB_SERVICES_USERNAME_DE}#"}, {"ParameterKey": "HtbServicesUsernameES", "ParameterValue": "#{HTB_SERVICES_USERNAME_ES}#"}, {"ParameterKey": "HtbServicesUsernameUK", "ParameterValue": "#{HTB_SERVICES_USERNAME_UK}#"}, {"ParameterKey": "HtbServicesUsernameNL", "ParameterValue": "#{HTB_SERVICES_USERNAME_NL}#"}, {"ParameterKey": "HtbServicesIdentifierGuest", "ParameterValue": "#{HTB_SERVICES_IDENTIFIER_GUEST}#"}, {"ParameterKey": "HtbServicesIdentifierDE", "ParameterValue": "#{HTB_SERVICES_IDENTIFIER_DE}#"}, {"ParameterKey": "HtbServicesIdentifierES", "ParameterValue": "#{HTB_SERVICES_IDENTIFIER_ES}#"}, {"ParameterKey": "HtbServicesIdentifierNL", "ParameterValue": "#{HTB_SERVICES_IDENTIFIER_NL}#"}, {"ParameterKey": "HtbServicesUsernameGuest", "ParameterValue": "#{HTB_SERVICES_USERNAME_GUEST}#"}, {"ParameterKey": "HtbXApiKey", "ParameterValue": "Htb-X-Api<PERSON>ey"}, {"ParameterKey": "HtbServicesPasswordGuest", "ParameterValue": "HtbServicesGuest-Password"}, {"ParameterKey": "DcsAccountUrl", "ParameterValue": "#{DCS_ACCOUNT_URL}#"}, {"ParameterKey": "DcsSasToken", "ParameterValue": "#{DCS_SAS_TOKEN}#"}, {"ParameterKey": "DcsDirectoryName", "ParameterValue": "#{DCS_DIRECTORY_NAME}#"}, {"ParameterKey": "DcsShareName", "ParameterValue": "#{DCS_SHARE_NAME}#"}, {"ParameterKey": "DcsOutboundRoamingBucketName", "ParameterValue": "#{DCS_BUCKET_NAME}#"}, {"ParameterKey": "DcsTokenClientId", "ParameterValue": "#{DCS_TOKEN_CLIENT_ID}#"}, {"ParameterKey": "DcsTokenClientSecret", "ParameterValue": "DcsTokenClient-Secret"}, {"ParameterKey": "DcsTokenResource", "ParameterValue": "#{DCS_TOKEN_RESOURCE}#"}, {"ParameterKey": "DcsTokenUrl", "ParameterValue": "#{DCS_TOKEN_URL}#"}, {"ParameterKey": "DcsSubscriptionKey", "ParameterValue": "DcsSubscription-Key"}, {"ParameterKey": "DcsFleetAccessKey", "ParameterValue": "DcsFleetAccess-Key"}, {"ParameterKey": "DcsFleetServicesUrl", "ParameterValue": "#{DCS_FLEET_SERVICES_URL}#"}, {"ParameterKey": "NLFleetGroup", "ParameterValue": "#{NL_FLEET_GROUP}#"}, {"ParameterKey": "DEFleetGroup", "ParameterValue": "#{DE_FLEET_GROUP}#"}, {"ParameterKey": "ESFleetGroup", "ParameterValue": "#{ES_FLEET_GROUP}#"}, {"ParameterKey": "S3Region", "ParameterValue": "#{S3_REGION}#"}, {"ParameterKey": "DcsCdrEventsGatewayStreamUrl", "ParameterValue": "#{DCS_CDR_EVENTS_GATEWAY_STREAM_URL}#"}, {"ParameterKey": "NodeEnv", "ParameterValue": "#{NODE_ENV}#"}, {"ParameterKey": "GuestChargeProcessingLambdaName", "ParameterValue": "#{GUEST_CHARGE_PROCESSING_LAMBDA_NAME}#"}, {"ParameterKey": "RegisteredChargeProcessingLambdaName", "ParameterValue": "#{REGISTERED_CHARGE_PROCESSING_LAMBDA_NAME}#"}, {"ParameterKey": "CreditCDRFeatureFlag", "ParameterValue": "#{CREDIT_CDR_FEATURE_FLAG}#"}, {"ParameterKey": "GoogleAnalyticsRoute", "ParameterValue": "#{GA_ROUTE}#"}, {"ParameterKey": "GoogleAnalyticsSecret", "ParameterValue": "GoogleAnalyticsSecret-ApiKey"}, {"ParameterKey": "GoogleAnalyticsMeasurementID", "ParameterValue": "#{GA_MEASUREMENT_ID}#"}, {"ParameterKey": "PushNotificationLambdaName", "ParameterValue": "#{PushNotificationLambdaName}#"}, {"ParameterKey": "ChargepointEventsKinesisShardCount", "ParameterValue": "#{ChargepointEventsKinesisShardCount}#"}, {"ParameterKey": "CurrentDate", "ParameterValue": "current_date_placeholder"}, {"ParameterKey": "ServiceVersion", "ParameterValue": "service_version_placeholder"}, {"ParameterKey": "ApolloClientUrl", "ParameterValue": "OTG_URL_V7"}, {"ParameterKey": "ApolloClientKey", "ParameterValue": "OTG_API_KEY_V7"}, {"ParameterKey": "ChargevisionTokenEndpoint", "ParameterValue": "CHARGEVISION_TOKEN_ENDPOINT"}, {"ParameterKey": "ChargevisionUrl", "ParameterValue": "CHARGEVISION_URL"}, {"ParameterKey": "ChargevisionAuthKey", "ParameterValue": "CHARGEVISION_AUTH_KEY"}, {"ParameterKey": "ChargepointAvailabilityCheckEnabledMarkets", "ParameterValue": "CHARGEPOINT_AVAILABILITY_CHECK_ENABLED_MARKETS"}, {"ParameterKey": "DeactivateDcsHtbTagsMaxConcurrentRequests", "ParameterValue": "#{DEACTIVATE_DCS_HTB_TAGS_MAX_CONCURRENT_REQUESTS}#"}]