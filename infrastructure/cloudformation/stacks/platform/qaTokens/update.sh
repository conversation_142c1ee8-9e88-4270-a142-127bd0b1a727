#!/bin/bash
cd lambdas
for lambda in $(ls); do cd $lambda; npm ci; cd ../; done
cd ../
aws cloudformation package --template-file template.yaml --s3-bucket ws-00f0-managed-stacks-test --s3-prefix manual/qaTokens/artifacts --output-template-file package.yaml --profile WS-00F0-role_DEVOPS --region eu-west-2
aws cloudformation update-stack --stack-name WS-00F0-QA-Tokens-vx-test --template-body file://package.yaml --parameters file://variables/variables_test.json --profile WS-00F0-role_DEVOPS --region eu-west-2 --capabilities CAPABILITY_AUTO_EXPAND
