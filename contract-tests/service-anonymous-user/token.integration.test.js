const axios = require('axios');

// run server at 4015
// require('../index');

const SERVER_TOKEN_URI =
  process.env.REACT_APP_ANONYMOUS_AUTH_TOKEN_URI ||
  'http://localhost:4015/api/initsession';
const SERVER_REFRESHABLE_TOKEN_URI =
  process.env.REACT_APP_ANONYMOUS_AUTH_REFRESHABLE_TOKEN_URI ||
  'http://localhost:4015/api/initrefreshsession';
const SERVER_TOKEN_REFRESH_URI =
  process.env.REACT_APP_ANONYMOUS_AUTH_SESSION_REFRESH_URI ||
  'http://localhost:4015/api/issuenewsessiontoken';
const SERVER_KEYS_URI =
  process.env.REACT_APP_ANONYMOUS_AUTH_KEYS_URI ||
  'http://localhost:4015/api/keys';
const API_KEY = process.env.API_GATEWAY_KEY || 'key';

axios.defaults.headers = axios.defaults.headers || {};
axios.defaults.headers.common = axios.defaults.headers.common || {};
axios.defaults.headers.common['x-api-key'] = API_KEY;
// packages used to obtain public key from store and decode and verify
const jwksClient = require('jwks-rsa');
const jwt = require('jsonwebtoken');
const client = jwksClient({
  jwksUri: SERVER_KEYS_URI,
  requestHeaders: { 'x-api-key': API_KEY },
});

// these headers aren't strictly necessary, just including them to valdiate the tracing
const headers = {
  jaegersession: '123456',
  jaegerrequest: '754632521',
  'jaeger-baggage': `session=123456, request=754632521`,
  'x-api-key': API_KEY,
};

describe('Auth server', () => {
  describe('Auth server keys endpoint', () => {
    it.skip('should return a json object containing a key', async () => {
      const { data: response } = await axios.get(SERVER_KEYS_URI).catch((e) => {
        console.log('axios error', e);
      });
      expect(response.keys).toHaveLength(1);
      expect(response.keys[0]).toHaveProperty('e');
      expect(response.keys[0]).toHaveProperty('n');
      expect(response.keys[0]).toHaveProperty('use');
      expect(response.keys[0]).toHaveProperty('kty');
    });
  });

  describe('Auth server initsession endpoint', () => {
    it.skip('should return a json object containing jwt', async () => {
      const response = await axios.get(SERVER_TOKEN_URI).catch((e) => {
        console.log('axios error', e);
      });
      expect(response.data).toHaveProperty('data');
      const splitJwt = response.data.data.split('.');
      expect(splitJwt).toHaveLength(3);
      const headers = JSON.parse(
        Buffer.from(splitJwt[0], 'base64').toString('utf8'),
      );
      expect(headers).toHaveProperty('alg');
      expect(headers).toHaveProperty('typ');
      expect(headers).toHaveProperty('kid');
      const payload = JSON.parse(
        Buffer.from(splitJwt[1], 'base64').toString('utf8'),
      );
      expect(payload).toHaveProperty('anonUserId');
    });
  });

  describe('Auth server initrefreshablesession endpoint', () => {
    it.skip('should return a json object containing jwt and also a refresh token as a secure cookie', async () => {
      const response = await axios
        .get(SERVER_REFRESHABLE_TOKEN_URI, {
          withCredentials: true,
          'x-api-key': API_KEY,
        })
        .catch((e) => {
          console.log('axios error', e);
          expect(true).toEqual(false);
        });
      expect(response.data).toHaveProperty('sessionToken');
      const splitJwt = response.data.sessionToken.split('.');
      expect(splitJwt).toHaveLength(3);
      const headers = JSON.parse(
        Buffer.from(splitJwt[0], 'base64').toString('utf8'),
      );
      expect(headers).toHaveProperty('alg');
      expect(headers).toHaveProperty('typ');
      expect(headers).toHaveProperty('kid');
      const payload = JSON.parse(
        Buffer.from(splitJwt[1], 'base64').toString('utf8'),
      );
      expect(payload).toHaveProperty('anonUserId');
      expect(
        response.headers['set-cookie'][0].includes('refreshToken'),
      ).toEqual(true);
      const refreshToken = response.headers['set-cookie'][0]
        .split(';')[0]
        .split('=')[1];
      const splitRefrshToken = refreshToken.split('.');
      expect(splitRefrshToken).toHaveLength(3);
    });
    it.skip('should be able to issue a new session token linked to the anon user with a valid refresh token', async () => {
      const initResponse = await axios
        .get(SERVER_REFRESHABLE_TOKEN_URI)
        .catch((e) => {
          console.log('axios error', e);
          expect(true).toEqual(false);
        });
      const refreshToken = initResponse.headers['set-cookie'][0]
        .split(';')[0]
        .split('=')[1];
      // axios.defaults.headers.common["x-api-key"] = API_KEY;
      const response = await axios
        .get(SERVER_TOKEN_REFRESH_URI, {
          headers: {
            Cookie: `refreshToken=${refreshToken};`,
            'x-api-key': API_KEY,
          },
        })
        .catch((e) => {
          console.log('axios error', e);
          expect(true).toEqual(false);
        });
      expect(response.data).toHaveProperty('sessionToken');
      const splitJwt = response.data.sessionToken.split('.');
      expect(splitJwt).toHaveLength(3);
      const headers = JSON.parse(
        Buffer.from(splitJwt[0], 'base64').toString('utf8'),
      );
      expect(headers).toHaveProperty('alg');
      expect(headers).toHaveProperty('typ');
      expect(headers).toHaveProperty('kid');
      const payload = JSON.parse(
        Buffer.from(splitJwt[1], 'base64').toString('utf8'),
      );
      expect(payload).toHaveProperty('anonUserId');
    });
  });

  describe('Verification process', () => {
    it.skip('should be possible to verify a token issues using the public key', async () => {
      // first request a token, and parse the headers to feed into the library that decodes the public key
      const { data: token } = await axios.get(SERVER_TOKEN_URI).catch((e) => {
        console.log('axios error', e);
      });

      const splitJwt = token.data.split('.');
      const headers = JSON.parse(
        Buffer.from(splitJwt[0], 'base64').toString('utf8'),
      );

      // this function requests the public singing key and decodes it
      const getKeyFromKeystore = (header) =>
        new Promise((resolve) => {
          client.getSigningKey(header.kid, (_, key) => {
            const signingKey = key.publicKey || key.rsaPublicKey;
            resolve(signingKey);
          });
        });

      const key = await getKeyFromKeystore(headers);

      // we then use the key with jwt.verify to verify the token we were issued
      const user = await new Promise((resolve) => {
        jwt.verify(
          token.data,
          key,
          {
            issuer: 'apollo-anon-auth',
            subject: 'user',
            audience: 'apollo',
            algorithms: ['RS256'],
          },
          (error, decoded) => {
            if (error) {
              resolve(null);
            }
            if (decoded) {
              resolve(decoded);
            }
          },
        );
      });
      expect(user).toHaveProperty('anonUserId');
    });
  });
});
