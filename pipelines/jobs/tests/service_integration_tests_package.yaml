parameters:
  - name: rootFolder
    type: string
  - name: environment
    type: string
  - name: needsRollback
    type: boolean
    default: false
  - name: deploymentName
    type: string
  - name: deploymentNamespace
    type: string

jobs:
  - job: service_integration_tests
    displayName: service integration tests
    steps:
      - template: ../../templates/AwsAuthenticationPs1.yaml
        parameters:
          IAMRole: '$(AwsLogicalAccountNameUpperCase)-role_AUTOMATION'

      - checkout: self
      - template: ../steps/node_install.yaml

      - task: npmAuthenticate@0
        displayName: 'npm authenticate: ${{ parameters.rootFolder }}'
        inputs:
          workingFile: '${{ parameters.rootFolder }}/.npmrc'

      - template: ../steps/npm_install.yaml

      - script: |
          GATEWAY_URL=$(echo "$PUBLIC_GATEWAY_SERVER_HTTP" | sed 's|\(https://[^/]*\)/.*|\1|')
          GATEWAY_USER_PATH=$(echo "$PRIVATE_GATEWAY_SERVER_HTTP_TESTS" | sed 's|https\?://[^/]*/|/|')
          GATEWAY_GUEST_PATH=$(echo "$PUBLIC_GATEWAY_SERVER_HTTP" | sed 's|https\?://[^/]*/|/|')

          echo "##vso[task.setvariable variable=successfulIntegrationTest]false"
          npm run test:service-integration
          if [ $? -ne 0 ]; then
            exit 1
          fi
          echo "##vso[task.setvariable variable=successfulIntegrationTest]true"
        displayName: service integration tests
        env:
          ENVIRONMENT: ${{ parameters.environment }}
          NODE_ENV: test
          G_MAPS_KEY: $(G_MAPS_KEY)
          QA_TOKEN_API_KEY: $(QA_TOKEN_API_KEY)
          GATEWAY_URL: $(GATEWAY_URL)
          GATEWAY_USER_PATH: $(GATEWAY_USER_PATH)
          GATEWAY_GUEST_PATH: $(GATEWAY_GUEST_PATH)
          API_GATEWAY_KEY: $(API_GATEWAY_KEY)
          SPREADLY_ENV_KEY: $(SPREADLY_ENV_KEY)
          TWILIO_AUTH_TOKEN: $(TWILIO_AUTH_TOKEN)
          FORGEROCK_PASSWORD: $(FORGEROCK_PASSWORD)
          FORGEROCK_CLIENT_SECRET: $(FORGEROCK_CLIENT_SECRET)
          GMAIL_APP_PASSWORD_BP_PULSE_TESTER: $(GMAIL_APP_PASSWORD_BP_PULSE_TESTER)
          CIP_CLIENT_SECRET: $(CIP_CLIENT_SECRET)
          CIP_USER_PASSWORD: $(CIP_USER_PASSWORD)

      - task: PublishTestResults@2
        condition: succeededOrFailed()
        inputs:
          testRunner: JUnit
          testResultsFiles: '**/svc-junit.xml'
          failTaskOnFailedTests: true
        displayName: publishing service test results

      - task: PublishBuildArtifacts@1
        condition: failed()
        inputs:
          pathToPublish: '$(System.DefaultWorkingDirectory)/service-integration-tests/temp/logs'
          artifactName: 'failed-test-screenshots'
          publishLocation: 'Container'
        displayName: Upload failed test screenshots

      - script: npm audit --production --audit-level=critical
        displayName: npm audit root

      - script: npm audit --production --audit-level=critical
        workingDirectory: ${{ parameters.rootFolder }}
        displayName: npm audit $(packageName)

      - ${{ if eq(parameters.needsRollback, true) }}:
          # Rollback if deployment or integration tests fail
          - script: |
              echo "##vso[task.setvariable variable=successfulRollback]false"
              aws eks --region $(AWSRegion) update-kubeconfig --name $(cluster-name)
              kubectl rollout undo deployment/${{ parameters.deploymentName }} -n ${{ parameters.deploymentNamespace }}
              kubectl get po -n ${{ parameters.deploymentNamespace }}
              echo "##vso[task.setvariable variable=successfulRollback]true"
            displayName: Rollback
            name: rollback
            condition: eq(variables.successfulIntegrationTest, false)

  - job: agent_cleanup
    displayName: Clean up AWS agent
    dependsOn: service_integration_tests

    condition: always()
    steps:
      - checkout: none
      - template: ./contract_tests/agent_cleanup.yaml
