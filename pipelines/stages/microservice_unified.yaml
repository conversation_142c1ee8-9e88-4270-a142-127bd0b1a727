stages:
  - stage: add_build_tag
    jobs:
      - job: add_build_tag_job
        steps:
          - checkout: none
          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                echo '${{ convertToJson(parameters.features) }}' > stacks.json
                jq -c '.[]' stacks.json | while read i; do
                  bool=$(echo "$i" | jq -r '.bool')
                  if [[ "$bool" == "True"  ||  "$bool" == "true" ]]; then
                    echo "Stack being deployed is: $(echo "$i" | jq -r '.deployName')"
                    echo "##vso[build.addbuildtag]$(echo "$i" | jq -r '.deployName')"
                  fi
                done
                echo "##vso[build.addbuildtag]${{ parameters.environment }}-ENV"
            displayName: 'Apply Build Tag'

  - ${{ if eq(parameters.environment, 'hotfix') }}:
      - stage: set_current_date
        displayName: 'Set current date parameter'
        dependsOn: add_build_tag
        jobs:
          - job: set_current_date_job
            steps:
              - checkout: none
              - script: |
                  echo "##vso[task.setvariable variable=formattedDate;isOutput=true]$(date +'%Y-%m-%d_%H-%M-%S')"
                name: set_current_date_script

  - ${{ if and(or(eq(parameters.environment, 'prod'), eq(parameters.environment, 'us-prod')), eq(parameters.snow_create_prod, true)) }}:
      - stage: output_deployed_prod_servers
        displayName: Output deployed prod servers
        jobs:
          - template: ../jobs/snow/output_template.yaml
            parameters:
              features: ${{ parameters.features }}
              gatewayFeatures: ${{ parameters.gatewayFeatures }}

      - stage: snow_create_cr
        displayName: 'Service Now Create Change Request'
        dependsOn:
          - output_deployed_prod_servers
        variables:
          - group: Pulse-Apps-Infrastructure-prod
          - name: finalMessage
            value: $[ stageDependencies.output_deployed_prod_servers.output_deployed_prod_servers_job.outputs['output_deployed_prod_servers_script.finalMessage'] ]
        jobs:
          - template: ../jobs/snow/create_cr.yaml
            parameters:
              ${{ if notIn(parameters.prodChangeCrDescription, 'Add reason for prod deployment if using automated CR', '', ' ') }}:
                finalMessage: |
                  $(finalMessage)
                  Additional info: ${{ parameters.prodChangeCrDescription }}
              ${{ else }}:
                finalMessage: '$(finalMessage)'
              Requester: 'BP Pulse'
              WorkItems: ${{ parameters.workItems }}
              prodChangeCrDescription: ${{ parameters.prodChangeCrDescription }}
              templateName: 'bp pulse eMSP production backend microservice deployment'

  - ${{ each feature in parameters.features }}:
      - ${{ if eq(feature.bool, true ) }}:
          - stage: clean_version_${{ feature.stageName }}
            displayName: Clean ${{ feature.stageName }} Microservice Version
            dependsOn:
              - add_build_tag
              - ${{ if eq(parameters.environment, 'hotfix')}}:
                  - set_current_date
              - ${{ if and(or(eq(parameters.environment, 'prod'), eq(parameters.environment, 'us-prod')), eq(parameters.snow_create_prod, true)) }}:
                  - snow_create_cr
            jobs:
              - template: ../jobs/steps/clean_unified_dropdown_version.yaml
                parameters:
                  deployTag: ${{ feature.deployTag }}
                  serviceVersion: ${{ feature.microserviceVersion }}

          - ${{ if eq(parameters.environment, 'hotfix')}}:
              - stage: build_${{ feature.stageName }}
                displayName: Build Image ${{ feature.stageName }}
                variables:
                  - group: Pulse-Apps-Microservices-Build-All-Envs
                  - group: Credentials-Apollo-PRD
                dependsOn:
                  - clean_version_${{ feature.stageName }}
                jobs:
                  - template: ../jobs/microservice/build.yaml
                    parameters:
                      tag: ${{ parameters.hotifx_commit }}
                      stageName: ${{ feature.stageName }}
                      featureName: ${{ feature.name }}
                      environment: preprod
                      pathName: ${{ feature.pathName }}

          - stage: release_create_${{ feature.stageName }}
            dependsOn:
              - clean_version_${{ feature.stageName }}
              - ${{ if eq(parameters.environment, 'hotfix')}}:
                  - set_current_date
                  - build_${{ feature.stageName }}
            variables:
              - group: Credentials-Apollo-PRD
              - group: Pulse-Apps-Microservices-Build-All-Envs
              - name: deployTag
                value: $[ stageDependencies.clean_version_${{ feature.stageName }}.clean_version_job.outputs['clean_version_script.deployTag'] ]
              - name: microserviceVersion
                value: $[ stageDependencies.clean_version_${{ feature.stageName }}.clean_version_job.outputs['clean_version_script.serviceVersion'] ]
              - ${{ if eq(parameters.environment, 'hotfix')}}:
                  - name: currentDate
                    value: $[ stageDependencies.set_current_date.set_current_date_job.outputs['set_current_date_script.formattedDate'] ]
            jobs:
              - template: ../jobs/microservice/image_tag.yaml
                parameters:
                  ${{ if eq(feature.deployTag, 'Custom' ) }}:
                    newImageTag: $(microserviceVersion).${{ parameters.environment }}
                    gitTag: ${{ feature.name }}.$(microserviceVersion)
                  ${{ if notIn(feature.deployTag, 'Custom', 'Hotfix' ) }}:
                    newImageTag: $(deployTag).${{ parameters.environment }}
                    gitTag: ${{ feature.name }}.$(deployTag)
                  ecrRepositoryName: ${{ feature.name }}
                  stageName: ${{ feature.stageName }}
                  ${{ if eq(feature.deployTag, 'Hotfix') }}:
                    newImageTag: $(currentDate).${{ parameters.environment }}
                    gitTag: ${{ parameters.hotifx_commit }}
                    environment: preprod
              - ${{ if eq(feature.deployTag, 'Hotfix') }}:
                  - template: ../jobs/microservice/image_tag.yaml
                    parameters:
                      newImageTag: $(currentDate).${{ parameters.environment }}.preprod
                      gitTag: ${{ parameters.hotifx_commit }}
                      environment: preprod
                      stageName: ${{ feature.stageName }}_Hotfix
                      ecrRepositoryName: ${{ feature.name }}

  - ${{ each feature in parameters.features }}:
      - ${{ if eq(feature.bool, true) }}:
          - stage: deploy_${{ feature.stageName }}_to_${{ replace(parameters.environment, '-', '_') }}
            dependsOn:
              - release_create_${{ feature.stageName }}
              - clean_version_${{ feature.stageName }}
              - ${{ if eq(parameters.environment, 'hotfix')}}:
                  - set_current_date
                  - build_${{ feature.stageName }}
            variables:
              - group: Pulse-Apps-Gateway-Variables
              - ${{ if eq(parameters.environment, 'hotfix')}}:
                  - group: Pulse-Apps-Microservices-preprod
              # Remove when prod lib groups are in place
              - ${{ if ne(parameters.environment, 'hotfix') }}:
                  - group: Pulse-Apps-Microservices-${{ parameters.environment }}
              - name: successfulDeployment
              - name: successfulRollback
              - ${{ if in(parameters.environment, 'preprod', 'performance', 'prod', 'hotfix', 'us-uat', 'us-prod') }}:
                  - group: Credentials-Apollo-PRD
              - ${{ else }}:
                  - group: Credentials-Apollo-QA
              - ${{ if eq(parameters.environment, 'hotfix')}}:
                  - name: currentDate
                    value: $[ stageDependencies.set_current_date.set_current_date_job.outputs['set_current_date_script.formattedDate'] ]
              - name: deployTag
                value: $[ stageDependencies.clean_version_${{ feature.stageName }}.clean_version_job.outputs['clean_version_script.deployTag'] ]
              - name: microserviceVersion
                value: $[ stageDependencies.clean_version_${{ feature.stageName }}.clean_version_job.outputs['clean_version_script.serviceVersion'] ]
            displayName: Deploy ${{ feature.stageName }} to ${{ parameters.environment }}
            jobs:
              - template: ../jobs/microservice/deploy.yaml #generic build job (no generate env vars steps)
                parameters:
                  apiVersion: ${{ parameters.apiVersion }}
                  featureName: ${{ feature.name }}
                  deploymentFolderPath: $(System.DefaultWorkingDirectory)/packages/${{ feature.name }}/deployment
                  deploymentFilePath: $(System.DefaultWorkingDirectory)/packages/${{ feature.name }}/deployment/${{ feature.name }}-manifest.yaml
                  deploymentName: ${{ feature.deploymentName }}-v${{ parameters.apiVersion }}
                  deploymentNamespace: ${{ feature.namespace }}
                  buildDependencyBool: False
                  buildStageName: 'none'
                  targetPort: ${{ feature.targetPort }}
                  server_name: ${{ feature.name }}
                  deployName: ${{ feature.deployName }}
                  hpapdbName: ${{ feature.hpapdbName }}
                  gateways: ${{ parameters.gatewayFeatures }}
                  name: ${{ feature.name }}
                  ${{ if eq(feature.deployTag, 'Custom' ) }}:
                    imageTag: $(microserviceVersion).${{ parameters.environment }}
                    gitTag: ${{ feature.name }}.$(microserviceVersion)
                  ${{ if notIn(feature.deployTag, 'Custom', 'Hotfix' ) }}:
                    imageTag: $(deployTag).${{ parameters.environment }}
                    gitTag: ${{ feature.name }}.$(deployTag)
                  ${{ if ne(feature.deployTag, 'Hotfix')}}:
                    env: ${{ parameters.environment }}
                  ${{ if eq(feature.deployTag, 'Hotfix') }}:
                    imageTag: $(currentDate).${{ parameters.environment }}
                    gitTag: ${{ parameters.hotifx_commit }}
                    env: preprod
                  ${{ if in(feature.name, 'charge-server', 'wallet-server', 'user-server', 'payments-bppay-server')}}:
                    minReplicas: $(HPA_REPLICAS_LARGE)
                    maxReplicas: $(REPLICAS_MAX_XTRA_LARGE)
                    averageUtilization: 95
                  ${{ if in(feature.name, 'map-server')}}:
                    minReplicas: $(HPA_REPLICAS_LARGE)
                    maxReplicas: $(REPLICAS_MAX_XTRA_LARGE)
                    averageUtilization: 80
                  ${{ if in(feature.name, 'anonymous-user-server' )}}:
                    minReplicas: $(HPA_REPLICAS_MEDIUM)
                    maxReplicas: $(REPLICAS_MAX_MEDIUM)
                    averageUtilization: 100
                  ${{ if in(feature.name, 'gateway-private-server')}}:
                    minReplicas: $(HPA_REPLICAS_LARGE)
                    maxReplicas: $(REPLICAS_MAX_GATEWAY_SERVER)
                    averageUtilization: 65
                  ${{ if in(feature.name, 'gateway-public-server')}}:
                    minReplicas: $(HPA_REPLICAS_LARGE)
                    maxReplicas: $(REPLICAS_MAX_GATEWAY_SERVER)
                    averageUtilization: 100
                  ${{ if in(feature.name, 'prices-server', 'invoices-server', 'favourites-server', 'voucher-server', 'payments-stripe-server', 'payments-gocardless-server', 'test-provider-server')}}:
                    minReplicas: $(HPA_REPLICAS_SMALL)
                    maxReplicas: $(REPLICAS_MAX_MEDIUM)
                    averageUtilization: 100
                  ${{ if in(feature.name, 'history-server', 'pdf-server')}}:
                    minReplicas: $(HPA_REPLICAS_SMALL)
                    maxReplicas: $(REPLICAS_MAX_MEDIUM)
                    averageUtilization: 65
                  ${{ if in(feature.name, 'offer-server', 'subscription-server' )}}:
                    minReplicas: $(HPA_REPLICAS_SMALL)
                    maxReplicas: $(REPLICAS_MAX_LARGE)
                    averageUtilization: 100
                  ${{ if in(feature.name, 'rfid-server' )}}:
                    minReplicas: $(HPA_REPLICAS_MEDIUM)
                    maxReplicas: $(REPLICAS_MAX_MEDIUM)
                    averageUtilization: 95
                  ${{ if in(feature.name, 'ocpi-server' )}}:
                    minReplicas: $(HPA_REPLICAS_MEDIUM)
                    maxReplicas: $(REPLICAS_MAX_LARGE)
                    averageUtilization: 100

  - ${{ if in(parameters.environment, 'test')}}:
      - stage: service_integration_tests
        dependsOn:
          - ${{ each feature in parameters.features }}:
              - ${{ if eq( feature.bool, 'true')}}:
                  - deploy_${{ feature.stageName }}_to_${{ parameters.environment }}
                  - clean_version_${{ feature.stageName }}
        variables:
          - group: Pulse-Apps-Microservices-${{ parameters.environment }}
          - group: bp-pulse-mobile-app-${{ parameters.environment }}
          - group: Credentials-Apollo-QA
          - group: Service_test_group
          - name: successfulIntegrationTest
        displayName: Service Integration Tests
        pool:
          name: ServiceIntergrationTest
        jobs:
          - template: ../jobs/tests/service_integration_tests_package.yaml
            parameters:
              rootFolder: $(System.DefaultWorkingDirectory)
              environment: ${{ parameters.environment }}
              needsRollback: true
              deploymentName: ${{ parameters.feature.deploymentName }}-v${{ parameters.apiVersion }}
              deploymentNamespace: ${{ parameters.feature.namespace }}

  - ${{ each feature in parameters.features }}:
      - ${{ if eq(feature.bool, true) }}:
          - stage: ${{ feature.stageName }}_git_tag
            displayName: 'Git Tag ${{ feature.stageName }}'
            dependsOn:
              - deploy_${{ feature.stageName }}_to_${{ replace(parameters.environment, '-', '_') }}
              - clean_version_${{ feature.stageName }}
              - ${{ if eq(parameters.environment, 'hotfix')}}:
                  - set_current_date
              - ${{ if in(parameters.environment, 'test')}}:
                  - service_integration_tests
            variables:
              - name: deployTag
                value: $[ stageDependencies.clean_version_${{ feature.stageName }}.clean_version_job.outputs['clean_version_script.deployTag'] ]
              - name: microserviceVersion
                value: $[ stageDependencies.clean_version_${{ feature.stageName }}.clean_version_job.outputs['clean_version_script.serviceVersion'] ]
              - ${{ if eq(parameters.environment, 'hotfix')}}:
                  - name: currentDate
                    value: $[ stageDependencies.set_current_date.set_current_date_job.outputs['set_current_date_script.formattedDate'] ]
            jobs:
              - template: ../jobs/steps/git_tag.yaml
                parameters:
                  infra: false
                  environment: ${{ parameters.environment }}
                  ${{ if eq(parameters.environment, 'hotfix')}}:
                    tag: ${{ parameters.hotifx_commit }}
                    newTag: ${{ feature.name }}.$(currentDate)
                  ${{ if eq(feature.deployTag, 'Custom' ) }}:
                    tag: ${{ feature.name }}.$(microserviceVersion)
                    newTag: ${{ feature.name }}.$(microserviceVersion)
                  ${{ if notIn(feature.deployTag, 'Custom', 'Hotfix' ) }}:
                    tag: ${{ feature.name }}.$(deployTag)
                    newTag: ${{ feature.name }}.$(deployTag)
                  name: ${{ feature.name }}
                  stageName: ${{ feature.stageName }}
          - ${{ if ne(parameters.environment, 'hotfix')}}:
              - stage: update_tags_${{ feature.stageName }}
                dependsOn:
                  - ${{ feature.stageName }}_git_tag
                  - deploy_${{ feature.stageName }}_to_${{ replace(parameters.environment, '-', '_') }}
                  - clean_version_${{ feature.stageName }}
                variables:
                  - group: Pulse-Apps-Microservices-test
                  - group: Credentials-Apollo-QA
                  - name: deployTag
                    value: $[ stageDependencies.clean_version_${{ feature.stageName }}.clean_version_job.outputs['clean_version_script.deployTag'] ]
                  - name: microserviceVersion
                    value: $[ stageDependencies.clean_version_${{ feature.stageName }}.clean_version_job.outputs['clean_version_script.serviceVersion'] ]
                displayName: 'Update Params ${{ feature.stageName }}'
                jobs:
                  - template: ../jobs/steps/updateDropdownTags.yaml
                    parameters:
                      environment: ${{ parameters.environment }}
                      ${{ if eq(feature.deployTag, 'Custom' ) }}:
                        tagToUpdate: $(microserviceVersion)
                      ${{ if ne(feature.deployTag, 'Custom' ) }}:
                        tagToUpdate: $(deployTag)
                      serviceTag: ${{ feature.stageName }}_tag
                      fileToEdit: 'microservice/pipeline_unified'

  - ${{ if in(parameters.environment, 'test')}}:
      - stage: security_api_tests
        displayName: api security tests
        dependsOn:
          - ${{ each feature in parameters.features }}:
              - ${{ if eq( feature.bool, 'true')}}:
                  - update_tags_${{ feature.stageName }}
                  - deploy_${{ feature.stageName }}_to_${{ parameters.environment }}
                  - clean_version_${{ feature.stageName }}
        variables:
          - group: Pulse-Apps-Microservices-test
        jobs:
          - template: ../jobs/tests/api_security_tests.yaml

  - ${{ if in(parameters.environment, 'prod')}}:
      - ${{ if eq(parameters.snow_create_prod, false) }}:
          - stage: snow_close_cr
            displayName: 'Service Now Close Change Request'
            dependsOn:
              - ${{ each feature in parameters.features }}:
                  - ${{ if eq(feature.bool, 'true') }}:
                      - deploy_${{ feature.stageName }}_to_prod
            jobs:
              - template: ../jobs/snow/close_cr.yaml
                parameters:
                  snowCR: ${{ parameters.snowCR }}

      - ${{ if eq(parameters.snow_create_prod, true) }}:
          - stage: snow_close_automated_cr
            displayName: 'Service Now Close Automated Change Request'
            condition: always()
            variables:
              changeRecordId: $[ stageDependencies.snow_create_cr.create_change_request.outputs['export_change_record_id.changeRecordId']]
            dependsOn:
              - ${{ each feature in parameters.features }}:
                  - ${{ if eq( feature.bool, 'true') }}:
                      - deploy_${{ feature.stageName }}_to_prod
              - snow_create_cr
            jobs:
              - template: ../jobs/snow/close_cr.yaml
                parameters:
                  snowCR: $(changeRecordId)
